import { useState } from 'react';
import { Dimensions, Pressable } from 'react-native';
import { View, Text, Card, Button, YStack } from 'tamagui';
import { MotiView } from 'moti';
import { suppliersData } from '~/temp-data/suppliersData';
import { router } from 'expo-router';
import { Image } from 'react-native';
import { MapboxPlaceholder } from '../MapboxPlaceholder';

type Addition = { id: string; name: string; price: number }

type Product = {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  }

type Supplier = {
        id: string;
        name: string;
        lat: number;
        lng: number;
        banner: string;
        logoUrl: string;
        rating: number;
        deliveryTime: string;
        openHours: string;
        tags: string[];
        phone: string;
        category: string;
        products: Product[];
    };

export default function SuppliersMap() {
  const { width, height } = Dimensions.get('window');
  const [selected, setSelected] = useState<Supplier | null>(null);

  return (
    <View flex={1}>
      <MapboxPlaceholder
        title="Suppliers Map"
        message="Interactive map showing supplier locations. Requires development build for Mapbox."
      />

      {selected && (
        <MotiView
          from={{ opacity: 0, translateY: 50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing' }}
          style={{ position: 'absolute', bottom: 80, left: 20, right: 20 }}
        >
          <Card p="$4" br="$6" elevate bw={1} bc="$gray5" bg="white">
            <YStack gap="$2">
              <Text fontSize="$6" fontWeight="700">{selected.name}</Text>
              <Text fontSize="$4" color="$gray9">{selected.category}</Text>
              <Button onPress={() => router.push({
                    pathname: "/home/<USER>",
                    params: { supplierId: selected.id }
              })}>
                View
              </Button>
              <Button variant="outlined" onPress={() => setSelected(null)}>Close</Button>
            </YStack>
          </Card>
        </MotiView>
      )}
    </View>
  );
}
