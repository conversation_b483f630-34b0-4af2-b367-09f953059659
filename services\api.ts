import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:5000/api/v1' 
  : 'https://api.wasel.com/api/v1';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token from storage:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
            refreshToken,
          });

          const { accessToken } = response.data.data;
          await AsyncStorage.setItem('accessToken', accessToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        await AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
        // You can emit an event here to redirect to login
        console.error('Token refresh failed:', refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

// User Types
export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  role: 'customer' | 'supplier' | 'admin' | 'delivery_driver';
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isActive: boolean;
  addresses: Address[];
  language: 'en' | 'ar';
  currency: 'USD' | 'ILS' | 'JOD';
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  _id?: string;
  type: 'home' | 'work' | 'other';
  label?: string;
  street: string;
  building?: string;
  floor?: string;
  apartment?: string;
  city: string;
  area: string;
  landmark?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  isDefault: boolean;
}

// Signup Data Types
export interface SignupStep1Data {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface SignupStep2Data {
  password: string;
  confirmPassword: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
}

export interface SignupStep3Data {
  address: Omit<Address, '_id'>;
}

export interface SignupStep4Data {
  language: 'en' | 'ar';
  currency: 'USD' | 'ILS' | 'JOD';
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
  };
}

export interface CompleteSignupData extends SignupStep1Data, SignupStep2Data, SignupStep3Data, SignupStep4Data {}

// Auth API Methods
export const authAPI = {
  // Signup
  signup: async (data: CompleteSignupData): Promise<ApiResponse<{ user: User; accessToken: string; refreshToken: string }>> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/signup', data);
    return response.data;
  },

  // Login
  login: async (emailOrPhone: string, password: string, fcmToken?: string): Promise<ApiResponse<{ user: User; accessToken: string; refreshToken: string }>> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/login', {
      emailOrPhone,
      password,
      fcmToken,
    });
    return response.data;
  },

  // Logout
  logout: async (): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/logout');
    return response.data;
  },

  // Verify Email
  verifyEmail: async (token: string): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/verify-email', { token });
    return response.data;
  },

  // Resend Email Verification
  resendEmailVerification: async (): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/resend-email-verification');
    return response.data;
  },

  // Verify Phone
  verifyPhone: async (code: string): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/verify-phone', { code });
    return response.data;
  },

  // Resend Phone Verification
  resendPhoneVerification: async (): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/resend-phone-verification');
    return response.data;
  },

  // Forgot Password
  forgotPassword: async (email: string): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/forgot-password', { email });
    return response.data;
  },

  // Reset Password
  resetPassword: async (token: string, password: string): Promise<ApiResponse> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/reset-password', { token, password });
    return response.data;
  },

  // Refresh Token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ accessToken: string }>> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/auth/refresh-token', { refreshToken });
    return response.data;
  },
};

// User API Methods
export const userAPI = {
  // Get Profile
  getProfile: async (): Promise<ApiResponse<User>> => {
    const response: AxiosResponse<ApiResponse> = await api.get('/users/profile');
    return response.data;
  },

  // Update Profile
  updateProfile: async (data: Partial<User>): Promise<ApiResponse<User>> => {
    const response: AxiosResponse<ApiResponse> = await api.put('/users/profile', data);
    return response.data;
  },

  // Add Address
  addAddress: async (address: Omit<Address, '_id'>): Promise<ApiResponse<User>> => {
    const response: AxiosResponse<ApiResponse> = await api.post('/users/addresses', address);
    return response.data;
  },

  // Update Address
  updateAddress: async (addressId: string, address: Partial<Address>): Promise<ApiResponse<User>> => {
    const response: AxiosResponse<ApiResponse> = await api.put(`/users/addresses/${addressId}`, address);
    return response.data;
  },

  // Delete Address
  deleteAddress: async (addressId: string): Promise<ApiResponse<User>> => {
    const response: AxiosResponse<ApiResponse> = await api.delete(`/users/addresses/${addressId}`);
    return response.data;
  },
};

// Storage helpers
export const storage = {
  // Save auth data
  saveAuthData: async (accessToken: string, refreshToken: string, user: User) => {
    try {
      await AsyncStorage.multiSet([
        ['accessToken', accessToken],
        ['refreshToken', refreshToken],
        ['user', JSON.stringify(user)],
      ]);
    } catch (error) {
      console.error('Error saving auth data:', error);
      throw error;
    }
  },

  // Get auth data
  getAuthData: async () => {
    try {
      const [accessToken, refreshToken, userString] = await AsyncStorage.multiGet([
        'accessToken',
        'refreshToken',
        'user',
      ]);

      return {
        accessToken: accessToken[1],
        refreshToken: refreshToken[1],
        user: userString[1] ? JSON.parse(userString[1]) : null,
      };
    } catch (error) {
      console.error('Error getting auth data:', error);
      return { accessToken: null, refreshToken: null, user: null };
    }
  },

  // Clear auth data
  clearAuthData: async () => {
    try {
      await AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
    } catch (error) {
      console.error('Error clearing auth data:', error);
      throw error;
    }
  },
};

export default api;
