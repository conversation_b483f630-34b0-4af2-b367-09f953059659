const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

config.resolver.assetExts = config.resolver.assetExts.filter(ext => ext !== 'css');

config.resolver.sourceExts = config.resolver.sourceExts.filter(ext => ext !== 'css');

// Platform-specific resolver to handle react-native-maps on web
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Custom resolver to handle platform-specific modules
const originalResolver = config.resolver.resolveRequest;
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Block react-native-maps and @rnmapbox/maps on web platform
  if (platform === 'web' && (moduleName === 'react-native-maps' || moduleName === '@rnmapbox/maps')) {
    return {
      type: 'empty',
    };
  }

  // Block CSS imports from mapbox-gl on web
  if (platform === 'web' && moduleName.includes('mapbox-gl/dist/mapbox-gl.css')) {
    return {
      type: 'empty',
    };
  }

  // Use default resolver for everything else
  if (originalResolver) {
    return originalResolver(context, moduleName, platform);
  }

  return context.resolveRequest(context, moduleName, platform);
};

module.exports = config;
