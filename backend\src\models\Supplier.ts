import mongoose, { Document, Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export interface IBusinessHours {
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  isOpen: boolean;
  openTime?: string; // Format: "HH:mm"
  closeTime?: string; // Format: "HH:mm"
  breaks?: {
    startTime: string;
    endTime: string;
  }[];
}

export interface ILocation {
  address: string;
  city: string;
  area: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  deliveryRadius: number; // in kilometers
}

export interface ISupplier extends Document {
  // Basic Information
  name: string;
  description: string;
  category: 'restaurants' | 'clothings' | 'supermarkets' | 'pharmacies' | 'electronics' | 'other';
  subcategory?: string;
  
  // Contact Information
  owner: mongoose.Types.ObjectId; // Reference to User
  email: string;
  phone: string;
  whatsapp?: string;
  website?: string;
  
  // Business Information
  businessLicense: string;
  taxId?: string;
  
  // Media
  logo?: string;
  banner?: string;
  gallery: string[];
  
  // Location & Delivery
  location: ILocation;
  businessHours: IBusinessHours[];
  
  // Service Information
  tags: string[];
  cuisineType?: string[]; // For restaurants
  deliveryTime: {
    min: number; // in minutes
    max: number; // in minutes
  };
  minimumOrder: number;
  deliveryFee: number;
  freeDeliveryThreshold?: number;
  
  // Ratings & Reviews
  rating: {
    average: number;
    count: number;
    breakdown: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
  };
  
  // Status & Verification
  isVerified: boolean;
  isActive: boolean;
  isFeatured: boolean;
  verificationStatus: 'pending' | 'approved' | 'rejected';
  verificationNotes?: string;
  
  // Financial
  commissionRate: number; // Percentage
  paymentMethods: ('cash' | 'card' | 'digital_wallet')[];
  
  // Analytics
  totalOrders: number;
  totalRevenue: number;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Methods
  isOpenNow(): boolean;
  getNextOpenTime(): Date | null;
  isInDeliveryRadius(lat: number, lng: number): boolean;
  calculateDeliveryFee(orderAmount: number, distance: number): number;
}

const BusinessHoursSchema = new Schema<IBusinessHours>({
  day: {
    type: String,
    enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    required: true
  },
  isOpen: {
    type: Boolean,
    default: true
  },
  openTime: {
    type: String,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
    required: function() { return this.isOpen; }
  },
  closeTime: {
    type: String,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
    required: function() { return this.isOpen; }
  },
  breaks: [{
    startTime: {
      type: String,
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    endTime: {
      type: String,
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    }
  }]
}, { _id: false });

const LocationSchema = new Schema<ILocation>({
  address: {
    type: String,
    required: true,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true
  },
  area: {
    type: String,
    required: true,
    trim: true
  },
  coordinates: {
    latitude: {
      type: Number,
      required: true,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      required: true,
      min: -180,
      max: 180
    }
  },
  deliveryRadius: {
    type: Number,
    required: true,
    min: 0,
    max: 50,
    default: 5
  }
}, { _id: false });

const SupplierSchema = new Schema<ISupplier>({
  name: {
    type: String,
    required: [true, 'Supplier name is required'],
    trim: true,
    minlength: [2, 'Supplier name must be at least 2 characters'],
    maxlength: [100, 'Supplier name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    enum: ['restaurants', 'clothings', 'supermarkets', 'pharmacies', 'electronics', 'other'],
    required: [true, 'Category is required']
  },
  subcategory: {
    type: String,
    trim: true
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number']
  },
  whatsapp: {
    type: String,
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid WhatsApp number']
  },
  website: {
    type: String,
    trim: true,
    match: [/^https?:\/\/.+/, 'Please enter a valid website URL']
  },
  businessLicense: {
    type: String,
    required: [true, 'Business license is required'],
    trim: true
  },
  taxId: {
    type: String,
    trim: true
  },
  logo: String,
  banner: String,
  gallery: [String],
  location: {
    type: LocationSchema,
    required: true
  },
  businessHours: [BusinessHoursSchema],
  tags: [{
    type: String,
    trim: true
  }],
  cuisineType: [{
    type: String,
    trim: true
  }],
  deliveryTime: {
    min: {
      type: Number,
      required: true,
      min: 0
    },
    max: {
      type: Number,
      required: true,
      min: 0
    }
  },
  minimumOrder: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  deliveryFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  freeDeliveryThreshold: {
    type: Number,
    min: 0
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0,
      min: 0
    },
    breakdown: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 }
    }
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  verificationNotes: String,
  commissionRate: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 10
  },
  paymentMethods: [{
    type: String,
    enum: ['cash', 'card', 'digital_wallet']
  }],
  totalOrders: {
    type: Number,
    default: 0,
    min: 0
  },
  totalRevenue: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Indexes
SupplierSchema.index({ name: 'text', description: 'text', tags: 'text' });
SupplierSchema.index({ category: 1 });
SupplierSchema.index({ 'location.coordinates': '2dsphere' });
SupplierSchema.index({ isActive: 1, isVerified: 1 });
SupplierSchema.index({ 'rating.average': -1 });
SupplierSchema.index({ isFeatured: -1, 'rating.average': -1 });

// Validate delivery time
SupplierSchema.pre('save', function(next) {
  if (this.deliveryTime.min > this.deliveryTime.max) {
    next(new Error('Minimum delivery time cannot be greater than maximum delivery time'));
  }
  next();
});

// Instance methods
SupplierSchema.methods.isOpenNow = function(): boolean {
  const now = new Date();
  const currentDay = now.toLocaleLowerCase().substring(0, 3) + 
    now.toLocaleLowerCase().substring(3);
  const currentTime = now.toTimeString().substring(0, 5);
  
  const todayHours = this.businessHours.find((hours: IBusinessHours) => 
    hours.day === currentDay
  );
  
  if (!todayHours || !todayHours.isOpen) {
    return false;
  }
  
  const isWithinHours = currentTime >= todayHours.openTime && 
    currentTime <= todayHours.closeTime;
  
  if (!isWithinHours) {
    return false;
  }
  
  // Check if currently in break time
  if (todayHours.breaks) {
    for (const breakTime of todayHours.breaks) {
      if (currentTime >= breakTime.startTime && currentTime <= breakTime.endTime) {
        return false;
      }
    }
  }
  
  return true;
};

SupplierSchema.methods.getNextOpenTime = function(): Date | null {
  // Implementation for getting next open time
  // This would be more complex in a real implementation
  return null;
};

SupplierSchema.methods.isInDeliveryRadius = function(lat: number, lng: number): boolean {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat - this.location.coordinates.latitude) * Math.PI / 180;
  const dLng = (lng - this.location.coordinates.longitude) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(this.location.coordinates.latitude * Math.PI / 180) *
    Math.cos(lat * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return distance <= this.location.deliveryRadius;
};

SupplierSchema.methods.calculateDeliveryFee = function(orderAmount: number, distance: number): number {
  if (this.freeDeliveryThreshold && orderAmount >= this.freeDeliveryThreshold) {
    return 0;
  }
  
  // Base delivery fee plus distance-based fee
  const distanceFee = Math.max(0, (distance - 2) * 0.5); // $0.5 per km after 2km
  return this.deliveryFee + distanceFee;
};

// Add pagination plugin
SupplierSchema.plugin(mongoosePaginate);

export const Supplier = mongoose.model<ISupplier>('Supplier', SupplierSchema);
