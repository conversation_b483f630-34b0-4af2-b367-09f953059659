import React, { useEffect } from 'react';
import { YStack, XStack, H4, Text, Progress, Theme, Button } from 'tamagui';
import { useSignupStore } from '../../store/signupStore';
import { useAuthStore } from '../../store/store';
import { SignupStep1 } from './signup-steps/SignupStep1';
import { SignupStep2 } from './signup-steps/SignupStep2';
import { SignupStep3 } from './signup-steps/SignupStep3';
import { SignupStep4 } from './signup-steps/SignupStep4';
import { EmailVerificationStep } from './signup-steps/EmailVerificationStep';
import { Alert } from 'react-native';
import { router } from 'expo-router';

export const MultiStepSignup = () => {
  const {
    currentStep,
    canProceedToNextStep,
    canProceedToPreviousStep,
    nextStep,
    previousStep,
    getCompleteSignupData,
    resetSignup,
  } = useSignupStore();

  const { signup, isLoading } = useAuthStore();

  // Step titles
  const stepTitles = [
    'Personal Info',
    'Account Details',
    'Address',
    'Preferences',
    'Email Verification',
  ];

  // Handle next step
  const handleNext = async () => {
    if (currentStep === 4) {
      // Complete signup on step 4
      await handleCompleteSignup();
    } else if (canProceedToNextStep()) {
      nextStep();
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    if (canProceedToPreviousStep()) {
      previousStep();
    }
  };

  // Complete signup
  const handleCompleteSignup = async () => {
    const signupData = getCompleteSignupData();
    
    if (!signupData) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      const result = await signup(signupData);
      
      if (result.success) {
        // Move to email verification step
        nextStep();
      } else {
        Alert.alert('Signup Failed', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  // Handle signup completion
  const handleSignupComplete = () => {
    resetSignup();
    router.replace('/(tabs)/home');
  };

  // Handle navigation to login
  const handleGoToLogin = () => {
    try {
      // Reset signup state first
      resetSignup();
      // Navigate to login page
      router.push('/');
    } catch (error) {
      console.error('Error navigating to login:', error);
    }
  };

  // Render current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <SignupStep1 />;
      case 2:
        return <SignupStep2 />;
      case 3:
        return <SignupStep3 />;
      case 4:
        return <SignupStep4 />;
      case 5:
        return <EmailVerificationStep onComplete={handleSignupComplete} />;
      default:
        return <SignupStep1 />;
    }
  };

  // Calculate progress
  const progress = (currentStep / 5) * 100;

  return (
    <Theme name="light">
      <YStack flex={1} padding="$4" gap="$4">
        {/* Header */}
        <YStack gap="$2">
          <H4 textAlign="center" color="$purple10">
            Create Account
          </H4>
          <Text textAlign="center" color="$gray10" fontSize="$3">
            Step {currentStep} of 5: {stepTitles[currentStep - 1]}
          </Text>
        </YStack>

        {/* Progress Bar */}
        <YStack gap="$2">
          <Progress value={progress} backgroundColor="$gray5">
            <Progress.Indicator backgroundColor="$purple10" />
          </Progress>
          <XStack justifyContent="space-between">
            {stepTitles.map((title, index) => (
              <Text
                key={index}
                fontSize="$1"
                color={index + 1 <= currentStep ? '$purple10' : '$gray8'}
                fontWeight={index + 1 === currentStep ? 'bold' : 'normal'}
              >
                {index + 1}
              </Text>
            ))}
          </XStack>
        </YStack>

        {/* Current Step Content */}
        <YStack flex={1}>
          {renderCurrentStep()}
        </YStack>

        {/* Navigation Buttons */}
        {currentStep < 5 && (
          <XStack gap="$3" justifyContent="space-between">
            <Button
              flex={1}
              variant="outlined"
              onPress={handlePrevious}
              disabled={!canProceedToPreviousStep()}
              opacity={canProceedToPreviousStep() ? 1 : 0.5}
            >
              Previous
            </Button>
            
            <Button
              flex={2}
              backgroundColor="$purple10"
              color="white"
              onPress={handleNext}
              disabled={!canProceedToNextStep() || isLoading}
              opacity={canProceedToNextStep() && !isLoading ? 1 : 0.5}
            >
              {isLoading ? 'Creating Account...' : currentStep === 4 ? 'Create Account' : 'Next'}
            </Button>
          </XStack>
        )}

        {/* Skip to Login */}
        <XStack justifyContent="center" paddingTop="$2">
          <Text color="$gray10" fontSize="$3">
            Already have an account?{' '}
          </Text>
          <Text
            color="$purple10"
            fontSize="$3"
            fontWeight="bold"
            onPress={handleGoToLogin}
          >
            Sign In
          </Text>
        </XStack>
      </YStack>
    </Theme>
  );
};
