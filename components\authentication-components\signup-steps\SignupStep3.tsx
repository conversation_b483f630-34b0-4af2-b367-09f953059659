import React, { useEffect, useState } from 'react';
import { YS<PERSON>ck, XStack, H5, <PERSON>, Button, Label } from 'tamagui';
import { useForm, FormProvider } from 'react-hook-form';
import { CustomTextField } from '../../CustomTextField';
import { useSignupStore, validateStep3 } from '../../../store/signupStore';
import { SignupStep3Data, Address } from '../../../services/api';
import { ExpoMapSelector } from './ExpoMapSelector';

export const SignupStep3 = () => {
  const { step3Data, updateStep3Data, setStepValid } = useSignupStore();
  const [showMap, setShowMap] = useState(false);
  const [selectedAddressType, setSelectedAddressType] = useState<'home' | 'work' | 'other'>('home');

  const methods = useForm<SignupStep3Data>({
    defaultValues: step3Data,
    mode: 'onChange',
  });

  const { watch, setValue, formState: { isValid } } = methods;
  const watchedValues = watch();

  // Update store when form values change
  useEffect(() => {
    updateStep3Data(watchedValues);
    const isStepValid = validateStep3(watchedValues);
    setStepValid(3, isStepValid);
  }, [watchedValues, updateStep3Data, setStepValid]);

  // Handle address type selection
  const handleAddressTypeSelect = (type: 'home' | 'work' | 'other') => {
    setSelectedAddressType(type);
    if (watchedValues.address) {
      setValue('address', {
        ...watchedValues.address,
        type,
      });
    }
  };

  // Handle map address selection
  const handleAddressSelect = (address: Omit<Address, '_id'>) => {
    setValue('address', {
      ...address,
      type: selectedAddressType,
      isDefault: true,
    });
    setShowMap(false);
  };

  // Handle manual address input
  const handleManualAddressChange = (field: keyof Address, value: string) => {
    if (watchedValues.address) {
      setValue('address', {
        ...watchedValues.address,
        [field]: value,
      });
    } else {
      setValue('address', {
        type: selectedAddressType,
        street: field === 'street' ? value : '',
        city: field === 'city' ? value : '',
        area: field === 'area' ? value : '',
        building: field === 'building' ? value : '',
        floor: field === 'floor' ? value : '',
        apartment: field === 'apartment' ? value : '',
        landmark: field === 'landmark' ? value : '',
        coordinates: { latitude: 0, longitude: 0 },
        isDefault: true,
        [field]: value,
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <YStack flex={1} gap="$4">
        {/* Header */}
        <YStack gap="$2" paddingBottom="$3">
          <H5 color="$purple10">Address Information</H5>
          <Text color="$gray10" fontSize="$3">
            Add your address for accurate delivery
          </Text>
        </YStack>

        {/* Address Type Selection */}
        <YStack gap="$2">
          <Label color="$gray11" fontSize="$3" fontWeight="600">
            Address Type
          </Label>
          <XStack gap="$2">
            {(['home', 'work', 'other'] as const).map((type) => (
              <Button
                key={type}
                flex={1}
                variant="outlined"
                backgroundColor={selectedAddressType === type ? '$purple3' : '$gray2'}
                borderColor={selectedAddressType === type ? '$purple8' : '$gray7'}
                onPress={() => handleAddressTypeSelect(type)}
              >
                <Text
                  color={selectedAddressType === type ? '$purple11' : '$gray11'}
                  textTransform="capitalize"
                >
                  {type}
                </Text>
              </Button>
            ))}
          </XStack>
        </YStack>

        {/* Map Selection Button */}
        <Button
          backgroundColor="$purple10"
          color="white"
          onPress={() => setShowMap(true)}
          icon="map"
        >
          Select Address from Map
        </Button>

        {/* Manual Address Input */}
        <YStack gap="$3">
          <Text color="$gray11" fontSize="$3" fontWeight="600">
            Or enter address manually:
          </Text>

          <CustomTextField
            name="address.street"
            icon="location"
            label="Street Address"
            placeholder="Enter street address"
            value={watchedValues.address?.street || ''}
            onChangeText={(value) => handleManualAddressChange('street', value)}
            rules={{
              required: 'Street address is required',
              minLength: {
                value: 3,
                message: 'Street address must be at least 3 characters',
              },
            }}
          />

          <XStack gap="$2">
            <YStack flex={1}>
              <CustomTextField
                name="address.building"
                icon="home"
                label="Building"
                placeholder="Building number"
                value={watchedValues.address?.building || ''}
                onChangeText={(value) => handleManualAddressChange('building', value)}
              />
            </YStack>
            <YStack flex={1}>
              <CustomTextField
                name="address.floor"
                icon="layers"
                label="Floor"
                placeholder="Floor number"
                value={watchedValues.address?.floor || ''}
                onChangeText={(value) => handleManualAddressChange('floor', value)}
              />
            </YStack>
          </XStack>

          <CustomTextField
            name="address.apartment"
            icon="home"
            label="Apartment"
            placeholder="Apartment number"
            value={watchedValues.address?.apartment || ''}
            onChangeText={(value) => handleManualAddressChange('apartment', value)}
          />

          <XStack gap="$2">
            <YStack flex={1}>
              <CustomTextField
                name="address.city"
                icon="location"
                label="City"
                placeholder="City"
                value={watchedValues.address?.city || ''}
                onChangeText={(value) => handleManualAddressChange('city', value)}
                rules={{
                  required: 'City is required',
                  minLength: {
                    value: 2,
                    message: 'City must be at least 2 characters',
                  },
                }}
              />
            </YStack>
            <YStack flex={1}>
              <CustomTextField
                name="address.area"
                icon="location"
                label="Area"
                placeholder="Area/District"
                value={watchedValues.address?.area || ''}
                onChangeText={(value) => handleManualAddressChange('area', value)}
                rules={{
                  required: 'Area is required',
                  minLength: {
                    value: 2,
                    message: 'Area must be at least 2 characters',
                  },
                }}
              />
            </YStack>
          </XStack>

          <CustomTextField
            name="address.landmark"
            icon="flag"
            label="Landmark (Optional)"
            placeholder="Nearby landmark"
            value={watchedValues.address?.landmark || ''}
            onChangeText={(value) => handleManualAddressChange('landmark', value)}
          />
        </YStack>

        {/* Selected Address Display */}
        {watchedValues.address && watchedValues.address.street && (
          <YStack gap="$2" padding="$3" backgroundColor="$purple2" borderRadius="$4">
            <Text color="$purple11" fontSize="$3" fontWeight="600">
              Selected Address:
            </Text>
            <Text color="$purple10" fontSize="$2">
              {watchedValues.address.street}
              {watchedValues.address.building && `, Building ${watchedValues.address.building}`}
              {watchedValues.address.floor && `, Floor ${watchedValues.address.floor}`}
              {watchedValues.address.apartment && `, Apt ${watchedValues.address.apartment}`}
            </Text>
            <Text color="$purple10" fontSize="$2">
              {watchedValues.address.area}, {watchedValues.address.city}
            </Text>
            {watchedValues.address.landmark && (
              <Text color="$purple9" fontSize="$2">
                Near: {watchedValues.address.landmark}
              </Text>
            )}
          </YStack>
        )}

        {/* Map Modal */}
        {showMap && (
          <ExpoMapSelector
            onAddressSelect={handleAddressSelect}
            onClose={() => setShowMap(false)}
          />
        )}
      </YStack>
    </FormProvider>
  );
};
