import React, { useEffect } from 'react';
import { YStack, H5, Text } from 'tamagui';
import { useForm, FormProvider } from 'react-hook-form';
import { CustomTextField } from '../../CustomTextField';
import { useSignupStore, validateStep1 } from '../../../store/signupStore';
import { SignupStep1Data } from '../../../services/api';

export const SignupStep1 = () => {
  const { step1Data, updateStep1Data, setStepValid } = useSignupStore();

  const methods = useForm<SignupStep1Data>({
    defaultValues: step1Data,
    mode: 'onChange',
  });

  const { watch, formState: { isValid } } = methods;
  const watchedValues = watch();

  // Update store when form values change
  useEffect(() => {
    updateStep1Data(watchedValues);
    const isStepValid = validateStep1(watchedValues);
    setStepValid(1, isStepValid);
  }, [watchedValues]); // Remove functions from dependency array to prevent infinite loops

  return (
    <FormProvider {...methods}>
      <YStack flex={1} gap="$4">
        {/* Header */}
        <YStack gap="$2" paddingBottom="$3">
          <H5 color="$purple10">Personal Information</H5>
          <Text color="$gray10" fontSize="$3">
            Let's start with your basic information
          </Text>
        </YStack>

        {/* Form Fields */}
        <YStack gap="$3">
          <CustomTextField
            name="firstName"
            icon="person"
            label="First Name"
            placeholder="Enter your first name"
            rules={{
              required: 'First name is required',
              minLength: {
                value: 2,
                message: 'First name must be at least 2 characters',
              },
              pattern: {
                value: /^[a-zA-Z\s]+$/,
                message: 'First name can only contain letters',
              },
            }}
          />

          <CustomTextField
            name="lastName"
            icon="person"
            label="Last Name"
            placeholder="Enter your last name"
            rules={{
              required: 'Last name is required',
              minLength: {
                value: 2,
                message: 'Last name must be at least 2 characters',
              },
              pattern: {
                value: /^[a-zA-Z\s]+$/,
                message: 'Last name can only contain letters',
              },
            }}
          />

          <CustomTextField
            name="email"
            icon="mail"
            label="Email Address"
            placeholder="Enter your email address"
            keyboardType="email-address"
            autoCapitalize="none"
            rules={{
              required: 'Email is required',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Please enter a valid email address',
              },
            }}
          />

          <CustomTextField
            name="phone"
            icon="call"
            label="Phone Number"
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            rules={{
              required: 'Phone number is required',
              minLength: {
                value: 10,
                message: 'Phone number must be at least 10 digits',
              },
              pattern: {
                value: /^[+]?[\d\s\-\(\)]+$/,
                message: 'Please enter a valid phone number',
              },
            }}
          />
        </YStack>

        {/* Info Text */}
        <YStack paddingTop="$3">
          <Text color="$gray9" fontSize="$2" textAlign="center">
            We'll use this information to create your account and keep you updated about your orders.
          </Text>
        </YStack>
      </YStack>
    </FormProvider>
  );
};
