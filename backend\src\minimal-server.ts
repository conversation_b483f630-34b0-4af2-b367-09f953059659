import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Basic middleware
app.use(cors({
  origin: ['http://localhost:19006', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// In-memory user storage for testing (replace with MongoDB later)
const users: any[] = [];

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Signup endpoint
app.post('/api/v1/auth/signup', async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      dateOfBirth,
      gender,
      address,
      preferences
    } = req.body;

    // Check if user already exists
    const existingUser = users.find(u => u.email === email || u.phone === phone);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email or phone'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = {
      id: Date.now().toString(),
      firstName,
      lastName,
      email,
      phone,
      password: hashedPassword,
      dateOfBirth,
      gender,
      address,
      preferences,
      isEmailVerified: false,
      isPhoneVerified: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    users.push(user);

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Remove password from response
    const { password: _, ...userResponse } = user;

    return res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        user: userResponse,
        token
      }
    });
  } catch (error) {
    console.error('Signup error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Login endpoint
app.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Remove password from response
    const { password: _, ...userResponse } = user;

    return res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Email verification endpoint (mock)
app.post('/api/v1/auth/verify-email', (req, res) => {
  const { email, code } = req.body;
  
  // Mock verification - always succeed for testing
  res.status(200).json({
    success: true,
    message: 'Email verified successfully'
  });
});

// Resend email verification endpoint (mock)
app.post('/api/v1/auth/resend-email-verification', (req, res) => {
  const { email } = req.body;
  
  // Mock resend - always succeed for testing
  res.status(200).json({
    success: true,
    message: 'Verification email sent successfully'
  });
});

// Get users endpoint (for testing)
app.get('/api/v1/auth/users', (req, res) => {
  const usersWithoutPasswords = users.map(({ password, ...user }) => user);
  res.status(200).json({
    success: true,
    data: usersWithoutPasswords
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Minimal auth server running on port ${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🔐 Signup: POST http://localhost:${PORT}/api/v1/auth/signup`);
  console.log(`🔑 Login: POST http://localhost:${PORT}/api/v1/auth/login`);
  console.log(`👥 Users: GET http://localhost:${PORT}/api/v1/auth/users`);
});

export default app;
