import express from 'express';
import rateLimit from 'express-rate-limit';
import {
  signup,
  login,
  logout,
  refreshToken,
  forgotPassword,
  resetPassword,
  verifyEmail,
  verifyPhone,
  resendEmailVerification,
  resendPhoneVerification
} from '../controllers/authController';
import { protect } from '../middleware/auth';

const router = express.Router();

// Rate limiting for auth routes
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const verificationLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // limit each IP to 3 verification attempts per windowMs
  message: {
    success: false,
    message: 'Too many verification attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - firstName
 *         - lastName
 *         - email
 *         - phone
 *         - password
 *       properties:
 *         firstName:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: User's first name
 *         lastName:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: User's last name
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         phone:
 *           type: string
 *           pattern: '^\\+?[1-9]\\d{1,14}$'
 *           description: User's phone number with country code
 *         password:
 *           type: string
 *           minLength: 8
 *           description: User's password (must contain uppercase, lowercase, number, and special character)
 *         dateOfBirth:
 *           type: string
 *           format: date
 *           description: User's date of birth
 *         gender:
 *           type: string
 *           enum: [male, female, other]
 *           description: User's gender
 *         language:
 *           type: string
 *           enum: [en, ar]
 *           default: en
 *           description: User's preferred language
 *         currency:
 *           type: string
 *           enum: [USD, ILS, JOD]
 *           default: USD
 *           description: User's preferred currency
 *         address:
 *           $ref: '#/components/schemas/Address'
 *         notifications:
 *           $ref: '#/components/schemas/NotificationPreferences'
 *         termsAccepted:
 *           type: boolean
 *           description: Whether user accepted terms and conditions
 *         privacyAccepted:
 *           type: boolean
 *           description: Whether user accepted privacy policy
 *         marketingConsent:
 *           type: boolean
 *           default: false
 *           description: Whether user consented to marketing communications
 *     
 *     Address:
 *       type: object
 *       required:
 *         - type
 *         - street
 *         - city
 *         - area
 *         - coordinates
 *       properties:
 *         type:
 *           type: string
 *           enum: [home, work, other]
 *         label:
 *           type: string
 *           maxLength: 50
 *         street:
 *           type: string
 *           minLength: 5
 *           maxLength: 200
 *         building:
 *           type: string
 *           maxLength: 50
 *         floor:
 *           type: string
 *           maxLength: 10
 *         apartment:
 *           type: string
 *           maxLength: 20
 *         city:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         area:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         landmark:
 *           type: string
 *           maxLength: 100
 *         coordinates:
 *           type: object
 *           required:
 *             - latitude
 *             - longitude
 *           properties:
 *             latitude:
 *               type: number
 *               minimum: -90
 *               maximum: 90
 *             longitude:
 *               type: number
 *               minimum: -180
 *               maximum: 180
 *     
 *     NotificationPreferences:
 *       type: object
 *       properties:
 *         email:
 *           type: boolean
 *           default: true
 *         sms:
 *           type: boolean
 *           default: true
 *         push:
 *           type: boolean
 *           default: true
 *         orderUpdates:
 *           type: boolean
 *           default: true
 *         promotions:
 *           type: boolean
 *           default: false
 *         newsletter:
 *           type: boolean
 *           default: false
 *     
 *     AuthResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *             tokens:
 *               type: object
 *               properties:
 *                 accessToken:
 *                   type: string
 *                 refreshToken:
 *                   type: string
 *     
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *         errors:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *               message:
 *                 type: string
 */

/**
 * @swagger
 * /api/v1/auth/signup:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             allOf:
 *               - $ref: '#/components/schemas/User'
 *               - type: object
 *                 required:
 *                   - confirmPassword
 *                 properties:
 *                   confirmPassword:
 *                     type: string
 *                     description: Password confirmation (must match password)
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: User already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/signup', authLimiter, signup);

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - emailOrPhone
 *               - password
 *             properties:
 *               emailOrPhone:
 *                 type: string
 *                 description: Email address or phone number
 *               password:
 *                 type: string
 *                 description: User's password
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token for push notifications
 *               rememberMe:
 *                 type: boolean
 *                 default: false
 *                 description: Whether to remember the user
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       401:
 *         description: Invalid credentials
 *       423:
 *         description: Account locked
 *       403:
 *         description: Account deactivated
 */
router.post('/login', authLimiter, login);

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: FCM token to remove
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', protect, logout);

/**
 * @swagger
 * /api/v1/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Valid refresh token
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', refreshToken);

/**
 * @swagger
 * /api/v1/auth/forgot-password:
 *   post:
 *     summary: Request password reset
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *     responses:
 *       200:
 *         description: Password reset email sent (if email exists)
 *       400:
 *         description: Validation error
 */
router.post('/forgot-password', authLimiter, forgotPassword);

/**
 * @swagger
 * /api/v1/auth/reset-password:
 *   post:
 *     summary: Reset password with token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - password
 *               - confirmPassword
 *             properties:
 *               token:
 *                 type: string
 *                 description: Password reset token
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 description: New password
 *               confirmPassword:
 *                 type: string
 *                 description: Password confirmation
 *     responses:
 *       200:
 *         description: Password reset successful
 *       400:
 *         description: Invalid or expired token
 */
router.post('/reset-password', authLimiter, resetPassword);

/**
 * @swagger
 * /api/v1/auth/verify-email:
 *   post:
 *     summary: Verify email address
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: Email verification token
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post('/verify-email', verificationLimiter, verifyEmail);

/**
 * @swagger
 * /api/v1/auth/verify-phone:
 *   post:
 *     summary: Verify phone number
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone
 *               - code
 *             properties:
 *               phone:
 *                 type: string
 *                 description: Phone number
 *               code:
 *                 type: string
 *                 pattern: '^\\d{6}$'
 *                 description: 6-digit verification code
 *     responses:
 *       200:
 *         description: Phone verified successfully
 *       400:
 *         description: Invalid or expired code
 */
router.post('/verify-phone', verificationLimiter, verifyPhone);

/**
 * @swagger
 * /api/v1/auth/resend-email-verification:
 *   post:
 *     summary: Resend email verification
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Verification email sent
 *       400:
 *         description: Email already verified
 *       401:
 *         description: Unauthorized
 */
router.post('/resend-email-verification', protect, verificationLimiter, resendEmailVerification);

/**
 * @swagger
 * /api/v1/auth/resend-phone-verification:
 *   post:
 *     summary: Resend phone verification
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Verification code sent
 *       400:
 *         description: Phone already verified
 *       401:
 *         description: Unauthorized
 */
router.post('/resend-phone-verification', protect, verificationLimiter, resendPhoneVerification);

export default router;
