import { create } from 'zustand';
import { SignupStep1Data, SignupStep2Data, SignupStep3Data, SignupStep4Data, CompleteSignupData } from '../services/api';

export interface SignupState {
  // Current step (1-5: steps 1-4 + email verification)
  currentStep: number;
  
  // Step data
  step1Data: Partial<SignupStep1Data>;
  step2Data: Partial<SignupStep2Data>;
  step3Data: Partial<SignupStep3Data>;
  step4Data: Partial<SignupStep4Data>;
  
  // Validation states
  step1Valid: boolean;
  step2Valid: boolean;
  step3Valid: boolean;
  step4Valid: boolean;
  
  // Email verification
  emailVerificationSent: boolean;
  emailVerified: boolean;
  
  // Actions
  setCurrentStep: (step: number) => void;
  updateStep1Data: (data: Partial<SignupStep1Data>) => void;
  updateStep2Data: (data: Partial<SignupStep2Data>) => void;
  updateStep3Data: (data: Partial<SignupStep3Data>) => void;
  updateStep4Data: (data: Partial<SignupStep4Data>) => void;
  setStepValid: (step: number, valid: boolean) => void;
  setEmailVerificationSent: (sent: boolean) => void;
  setEmailVerified: (verified: boolean) => void;
  getCompleteSignupData: () => CompleteSignupData | null;
  resetSignup: () => void;
  canProceedToNextStep: () => boolean;
  canProceedToPreviousStep: () => boolean;
  nextStep: () => void;
  previousStep: () => void;
}

export const useSignupStore = create<SignupState>((set, get) => ({
  // Initial state
  currentStep: 1,
  
  step1Data: {},
  step2Data: {},
  step3Data: {},
  step4Data: {
    language: 'en',
    currency: 'USD',
    notifications: {
      email: true,
      sms: true,
      push: true,
      orderUpdates: true,
      promotions: false,
      newsletter: false,
    },
  },
  
  step1Valid: false,
  step2Valid: false,
  step3Valid: false,
  step4Valid: false,
  
  emailVerificationSent: false,
  emailVerified: false,

  // Set current step
  setCurrentStep: (step: number) => {
    set({ currentStep: Math.max(1, Math.min(5, step)) });
  },

  // Update step data
  updateStep1Data: (data: Partial<SignupStep1Data>) => {
    set((state) => ({
      step1Data: { ...state.step1Data, ...data },
    }));
  },

  updateStep2Data: (data: Partial<SignupStep2Data>) => {
    set((state) => ({
      step2Data: { ...state.step2Data, ...data },
    }));
  },

  updateStep3Data: (data: Partial<SignupStep3Data>) => {
    set((state) => ({
      step3Data: { ...state.step3Data, ...data },
    }));
  },

  updateStep4Data: (data: Partial<SignupStep4Data>) => {
    set((state) => ({
      step4Data: { ...state.step4Data, ...data },
    }));
  },

  // Set step validation
  setStepValid: (step: number, valid: boolean) => {
    switch (step) {
      case 1:
        set({ step1Valid: valid });
        break;
      case 2:
        set({ step2Valid: valid });
        break;
      case 3:
        set({ step3Valid: valid });
        break;
      case 4:
        set({ step4Valid: valid });
        break;
    }
  },

  // Email verification
  setEmailVerificationSent: (sent: boolean) => {
    set({ emailVerificationSent: sent });
  },

  setEmailVerified: (verified: boolean) => {
    set({ emailVerified: verified });
  },

  // Get complete signup data
  getCompleteSignupData: (): CompleteSignupData | null => {
    const state = get();
    const { step1Data, step2Data, step3Data, step4Data } = state;

    // Validate all required fields
    if (
      !step1Data.firstName ||
      !step1Data.lastName ||
      !step1Data.email ||
      !step1Data.phone ||
      !step2Data.password ||
      !step3Data.address ||
      !step4Data.language ||
      !step4Data.currency ||
      !step4Data.notifications
    ) {
      return null;
    }

    return {
      // Step 1
      firstName: step1Data.firstName,
      lastName: step1Data.lastName,
      email: step1Data.email,
      phone: step1Data.phone,
      
      // Step 2
      password: step2Data.password,
      confirmPassword: step2Data.confirmPassword || step2Data.password,
      dateOfBirth: step2Data.dateOfBirth,
      gender: step2Data.gender,
      
      // Step 3
      address: step3Data.address,
      
      // Step 4
      language: step4Data.language,
      currency: step4Data.currency,
      notifications: step4Data.notifications,
    };
  },

  // Reset signup
  resetSignup: () => {
    set({
      currentStep: 1,
      step1Data: {},
      step2Data: {},
      step3Data: {},
      step4Data: {
        language: 'en',
        currency: 'USD',
        notifications: {
          email: true,
          sms: true,
          push: true,
          orderUpdates: true,
          promotions: false,
          newsletter: false,
        },
      },
      step1Valid: false,
      step2Valid: false,
      step3Valid: false,
      step4Valid: false,
      emailVerificationSent: false,
      emailVerified: false,
    });
  },

  // Navigation helpers
  canProceedToNextStep: (): boolean => {
    const state = get();
    switch (state.currentStep) {
      case 1:
        return state.step1Valid;
      case 2:
        return state.step2Valid;
      case 3:
        return state.step3Valid;
      case 4:
        return state.step4Valid;
      case 5:
        return state.emailVerified;
      default:
        return false;
    }
  },

  canProceedToPreviousStep: (): boolean => {
    const state = get();
    return state.currentStep > 1;
  },

  nextStep: () => {
    const state = get();
    const canProceed = (() => {
      switch (state.currentStep) {
        case 1:
          return state.step1Valid;
        case 2:
          return state.step2Valid;
        case 3:
          return state.step3Valid;
        case 4:
          return state.step4Valid;
        case 5:
          return state.emailVerified;
        default:
          return false;
      }
    })();

    if (canProceed && state.currentStep < 5) {
      set({ currentStep: state.currentStep + 1 });
    }
  },

  previousStep: () => {
    const state = get();
    if (state.currentStep > 1) {
      set({ currentStep: state.currentStep - 1 });
    }
  },
}));

// Validation helpers
export const validateStep1 = (data: Partial<SignupStep1Data>): boolean => {
  return !!(
    data.firstName &&
    data.firstName.trim().length >= 2 &&
    data.lastName &&
    data.lastName.trim().length >= 2 &&
    data.email &&
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email) &&
    data.phone &&
    data.phone.trim().length >= 10
  );
};

export const validateStep2 = (data: Partial<SignupStep2Data>): boolean => {
  return !!(
    data.password &&
    data.password.length >= 8 &&
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(data.password) &&
    data.confirmPassword &&
    data.password === data.confirmPassword
  );
};

export const validateStep3 = (data: Partial<SignupStep3Data>): boolean => {
  return !!(
    data.address &&
    data.address.street &&
    data.address.street.trim().length >= 3 &&
    data.address.city &&
    data.address.city.trim().length >= 2 &&
    data.address.area &&
    data.address.area.trim().length >= 2 &&
    data.address.coordinates &&
    typeof data.address.coordinates.latitude === 'number' &&
    typeof data.address.coordinates.longitude === 'number'
  );
};

export const validateStep4 = (data: Partial<SignupStep4Data>): boolean => {
  return !!(
    data.language &&
    ['en', 'ar'].includes(data.language) &&
    data.currency &&
    ['USD', 'ILS', 'JOD'].includes(data.currency) &&
    data.notifications &&
    typeof data.notifications.email === 'boolean' &&
    typeof data.notifications.sms === 'boolean' &&
    typeof data.notifications.push === 'boolean' &&
    typeof data.notifications.orderUpdates === 'boolean' &&
    typeof data.notifications.promotions === 'boolean' &&
    typeof data.notifications.newsletter === 'boolean'
  );
};
