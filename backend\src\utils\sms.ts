import twilio from 'twilio';
import { logger } from './logger';

interface SMSOptions {
  to: string;
  message: string;
}

// Initialize Twilio client
const getTwilioClient = () => {
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  
  if (!accountSid || !authToken) {
    throw new Error('Twilio credentials not configured');
  }
  
  return twilio(accountSid, authToken);
};

// Send SMS function
export const sendSMS = async (options: SMSOptions): Promise<void> => {
  try {
    const client = getTwilioClient();
    const fromNumber = process.env.TWILIO_PHONE_NUMBER;
    
    if (!fromNumber) {
      throw new Error('Twilio phone number not configured');
    }
    
    // Send SMS
    const message = await client.messages.create({
      body: options.message,
      from: fromNumber,
      to: options.to
    });
    
    logger.info(`SMS sent successfully to ${options.to}`, {
      messageSid: message.sid,
      status: message.status
    });
    
  } catch (error) {
    logger.error('Failed to send SMS:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      to: options.to
    });
    throw error;
  }
};

// Send verification code SMS
export const sendVerificationSMS = async (phone: string, code: string): Promise<void> => {
  const message = `Your Wasel verification code is: ${code}. Valid for 10 minutes. Do not share this code with anyone.`;
  
  await sendSMS({
    to: phone,
    message
  });
};

// Send order update SMS
export const sendOrderUpdateSMS = async (phone: string, orderNumber: string, status: string): Promise<void> => {
  let message = '';
  
  switch (status) {
    case 'confirmed':
      message = `Your Wasel order #${orderNumber} has been confirmed and is being prepared.`;
      break;
    case 'preparing':
      message = `Your Wasel order #${orderNumber} is being prepared by the restaurant.`;
      break;
    case 'ready':
      message = `Your Wasel order #${orderNumber} is ready for pickup/delivery.`;
      break;
    case 'picked_up':
      message = `Your Wasel order #${orderNumber} has been picked up by the delivery driver.`;
      break;
    case 'delivered':
      message = `Your Wasel order #${orderNumber} has been delivered. Thank you for choosing Wasel!`;
      break;
    case 'cancelled':
      message = `Your Wasel order #${orderNumber} has been cancelled. If you have any questions, please contact support.`;
      break;
    default:
      message = `Your Wasel order #${orderNumber} status has been updated to: ${status}`;
  }
  
  await sendSMS({
    to: phone,
    message
  });
};

// Send promotional SMS
export const sendPromotionalSMS = async (phone: string, promotion: string): Promise<void> => {
  const message = `🎉 Special offer from Wasel! ${promotion}. Order now and save! Reply STOP to opt out.`;
  
  await sendSMS({
    to: phone,
    message
  });
};

// Verify SMS configuration
export const verifySMSConfig = async (): Promise<boolean> => {
  try {
    const client = getTwilioClient();
    
    // Verify account by fetching account details
    const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    
    if (account.status === 'active') {
      logger.info('SMS configuration verified successfully');
      return true;
    } else {
      logger.error('Twilio account is not active');
      return false;
    }
    
  } catch (error) {
    logger.error('SMS configuration verification failed:', error);
    return false;
  }
};

// Format phone number for international use
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  let cleaned = phone.replace(/\D/g, '');
  
  // Add + if not present and number doesn't start with country code
  if (!phone.startsWith('+')) {
    // Assume US/Canada if 10 digits, otherwise add + prefix
    if (cleaned.length === 10) {
      cleaned = '1' + cleaned;
    }
    cleaned = '+' + cleaned;
  } else {
    cleaned = phone;
  }
  
  return cleaned;
};

// Validate phone number format
export const isValidPhoneNumber = (phone: string): boolean => {
  // Basic international phone number validation
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// Get SMS delivery status
export const getSMSStatus = async (messageSid: string): Promise<string | null> => {
  try {
    const client = getTwilioClient();
    const message = await client.messages(messageSid).fetch();
    return message.status;
  } catch (error) {
    logger.error('Failed to get SMS status:', error);
    return null;
  }
};

// Send bulk SMS (for notifications, promotions, etc.)
export const sendBulkSMS = async (recipients: string[], message: string): Promise<void> => {
  const client = getTwilioClient();
  const fromNumber = process.env.TWILIO_PHONE_NUMBER;
  
  if (!fromNumber) {
    throw new Error('Twilio phone number not configured');
  }
  
  const promises = recipients.map(async (phone) => {
    try {
      const result = await client.messages.create({
        body: message,
        from: fromNumber,
        to: phone
      });
      
      logger.info(`Bulk SMS sent to ${phone}`, { messageSid: result.sid });
      return { phone, success: true, messageSid: result.sid };
    } catch (error) {
      logger.error(`Failed to send bulk SMS to ${phone}:`, error);
      return { phone, success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });
  
  const results = await Promise.allSettled(promises);
  
  const successful = results.filter(result => result.status === 'fulfilled').length;
  const failed = results.filter(result => result.status === 'rejected').length;
  
  logger.info(`Bulk SMS completed: ${successful} successful, ${failed} failed`);
};
