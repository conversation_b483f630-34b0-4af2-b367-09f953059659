import mongoose, { Document, Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export interface IOrderItem {
  product: mongoose.Types.ObjectId;
  name: string;
  price: number;
  quantity: number;
  variant?: {
    id: string;
    name: string;
    price: number;
    attributes: any;
  };
  additions?: Array<{
    id: string;
    name: string;
    price: number;
    quantity: number;
  }>;
  without?: string[];
  specialInstructions?: string;
  subtotal: number;
}

export interface IDeliveryInfo {
  address: {
    type: 'home' | 'work' | 'other';
    label?: string;
    street: string;
    building?: string;
    floor?: string;
    apartment?: string;
    city: string;
    area: string;
    landmark?: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  deliveryInstructions?: string;
  contactPhone?: string;
  estimatedDeliveryTime?: Date;
  actualDeliveryTime?: Date;
}

export interface IPaymentInfo {
  method: 'cash' | 'card' | 'digital_wallet';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  transactionId?: string;
  cardLast4?: string;
  paymentGateway?: string;
  paidAt?: Date;
  refundedAt?: Date;
  refundAmount?: number;
  refundReason?: string;
}

export interface IOrder extends Document {
  // Basic Information
  orderNumber: string;
  customer: mongoose.Types.ObjectId;
  supplier: mongoose.Types.ObjectId;
  
  // Order Items
  items: IOrderItem[];
  
  // Pricing
  subtotal: number;
  deliveryFee: number;
  serviceFee: number;
  tax: number;
  discount: number;
  discountCode?: string;
  total: number;
  
  // Status
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'picked_up' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  
  // Delivery Information
  deliveryInfo: IDeliveryInfo;
  deliveryType: 'delivery' | 'pickup';
  
  // Payment Information
  paymentInfo: IPaymentInfo;
  
  // Driver Information
  driver?: mongoose.Types.ObjectId;
  driverAssignedAt?: Date;
  
  // Timing
  estimatedPreparationTime?: number; // in minutes
  estimatedDeliveryTime?: Date;
  actualDeliveryTime?: Date;
  
  // Communication
  customerNotes?: string;
  supplierNotes?: string;
  driverNotes?: string;
  
  // Tracking
  statusHistory: Array<{
    status: string;
    timestamp: Date;
    note?: string;
    updatedBy?: mongoose.Types.ObjectId;
  }>;
  
  // Rating & Review
  rating?: {
    food: number;
    delivery: number;
    overall: number;
    comment?: string;
    ratedAt: Date;
  };
  
  // Cancellation
  cancellationReason?: string;
  cancelledBy?: mongoose.Types.ObjectId;
  cancelledAt?: Date;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Methods
  calculateTotal(): number;
  updateStatus(status: string, note?: string, updatedBy?: mongoose.Types.ObjectId): Promise<void>;
  canBeCancelled(): boolean;
  getEstimatedDeliveryTime(): Date;
}

const OrderItemSchema = new Schema<IOrderItem>({
  product: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  variant: {
    id: String,
    name: String,
    price: Number,
    attributes: Schema.Types.Mixed
  },
  additions: [{
    id: { type: String, required: true },
    name: { type: String, required: true },
    price: { type: Number, required: true, min: 0 },
    quantity: { type: Number, required: true, min: 1 }
  }],
  without: [String],
  specialInstructions: {
    type: String,
    maxlength: 500
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  }
}, { _id: false });

const DeliveryInfoSchema = new Schema<IDeliveryInfo>({
  address: {
    type: {
      type: String,
      enum: ['home', 'work', 'other'],
      required: true
    },
    label: String,
    street: {
      type: String,
      required: true,
      trim: true
    },
    building: String,
    floor: String,
    apartment: String,
    city: {
      type: String,
      required: true,
      trim: true
    },
    area: {
      type: String,
      required: true,
      trim: true
    },
    landmark: String,
    coordinates: {
      latitude: {
        type: Number,
        required: true,
        min: -90,
        max: 90
      },
      longitude: {
        type: Number,
        required: true,
        min: -180,
        max: 180
      }
    }
  },
  deliveryInstructions: {
    type: String,
    maxlength: 500
  },
  contactPhone: String,
  estimatedDeliveryTime: Date,
  actualDeliveryTime: Date
}, { _id: false });

const PaymentInfoSchema = new Schema<IPaymentInfo>({
  method: {
    type: String,
    enum: ['cash', 'card', 'digital_wallet'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'refunded'],
    default: 'pending'
  },
  transactionId: String,
  cardLast4: String,
  paymentGateway: String,
  paidAt: Date,
  refundedAt: Date,
  refundAmount: {
    type: Number,
    min: 0
  },
  refundReason: String
}, { _id: false });

const OrderSchema = new Schema<IOrder>({
  orderNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  customer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  supplier: {
    type: Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true
  },
  items: {
    type: [OrderItemSchema],
    required: true,
    validate: {
      validator: function(items: IOrderItem[]) {
        return items.length > 0;
      },
      message: 'Order must have at least one item'
    }
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  deliveryFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  serviceFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  tax: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  discount: {
    type: Number,
    min: 0,
    default: 0
  },
  discountCode: String,
  total: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  deliveryInfo: {
    type: DeliveryInfoSchema,
    required: true
  },
  deliveryType: {
    type: String,
    enum: ['delivery', 'pickup'],
    default: 'delivery'
  },
  paymentInfo: {
    type: PaymentInfoSchema,
    required: true
  },
  driver: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  driverAssignedAt: Date,
  estimatedPreparationTime: {
    type: Number,
    min: 0
  },
  estimatedDeliveryTime: Date,
  actualDeliveryTime: Date,
  customerNotes: {
    type: String,
    maxlength: 1000
  },
  supplierNotes: {
    type: String,
    maxlength: 1000
  },
  driverNotes: {
    type: String,
    maxlength: 1000
  },
  statusHistory: [{
    status: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  rating: {
    food: {
      type: Number,
      min: 1,
      max: 5
    },
    delivery: {
      type: Number,
      min: 1,
      max: 5
    },
    overall: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: 1000
    },
    ratedAt: Date
  },
  cancellationReason: String,
  cancelledBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  cancelledAt: Date
}, {
  timestamps: true
});

// Indexes
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ customer: 1, createdAt: -1 });
OrderSchema.index({ supplier: 1, createdAt: -1 });
OrderSchema.index({ driver: 1, status: 1 });
OrderSchema.index({ status: 1, createdAt: -1 });
OrderSchema.index({ 'deliveryInfo.address.coordinates': '2dsphere' });

// Generate order number before saving
OrderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await mongoose.model('Order').countDocuments();
    this.orderNumber = `WAS${String(count + 1).padStart(6, '0')}`;
    
    // Add initial status to history
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      note: 'Order created'
    });
  }
  next();
});

// Instance methods
OrderSchema.methods.calculateTotal = function(): number {
  const subtotal = this.subtotal;
  const fees = this.deliveryFee + this.serviceFee + this.tax;
  return subtotal + fees - this.discount;
};

OrderSchema.methods.updateStatus = async function(
  status: string, 
  note?: string, 
  updatedBy?: mongoose.Types.ObjectId
): Promise<void> {
  this.status = status;
  this.statusHistory.push({
    status,
    timestamp: new Date(),
    note,
    updatedBy
  });
  
  // Update specific timestamps based on status
  if (status === 'delivered') {
    this.actualDeliveryTime = new Date();
  } else if (status === 'cancelled') {
    this.cancelledAt = new Date();
  }
  
  await this.save();
};

OrderSchema.methods.canBeCancelled = function(): boolean {
  const cancellableStatuses = ['pending', 'confirmed'];
  return cancellableStatuses.includes(this.status);
};

OrderSchema.methods.getEstimatedDeliveryTime = function(): Date {
  if (this.estimatedDeliveryTime) {
    return this.estimatedDeliveryTime;
  }
  
  // Calculate based on preparation time + delivery time
  const now = new Date();
  const prepTime = this.estimatedPreparationTime || 30; // default 30 minutes
  const deliveryTime = 20; // default 20 minutes delivery
  
  return new Date(now.getTime() + (prepTime + deliveryTime) * 60000);
};

// Add pagination plugin
OrderSchema.plugin(mongoosePaginate);

export const Order = mongoose.model<IOrder>('Order', OrderSchema);
