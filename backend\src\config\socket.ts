import { Server as SocketIOServer } from 'socket.io';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';
import { logger } from '../utils/logger';

interface AuthenticatedSocket extends SocketIOServer {
  userId?: string;
  user?: any;
}

export const setupSocketIO = (io: SocketIOServer): void => {
  // Authentication middleware for Socket.IO
  io.use(async (socket: any, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
      
      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string };
      
      // Get user from database
      const user = await User.findById(decoded.userId);
      if (!user || !user.isActive) {
        return next(new Error('Authentication error: Invalid user'));
      }

      // Attach user to socket
      socket.userId = user._id.toString();
      socket.user = user;
      
      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  // Handle connections
  io.on('connection', (socket: any) => {
    logger.info(`User connected: ${socket.userId}`);
    
    // Join user to their personal room
    socket.join(`user:${socket.userId}`);
    
    // Join user to role-based rooms
    if (socket.user.role === 'delivery_driver') {
      socket.join('drivers');
    } else if (socket.user.role === 'supplier') {
      socket.join('suppliers');
    } else if (socket.user.role === 'admin') {
      socket.join('admins');
    }

    // Handle order tracking
    socket.on('track_order', (orderId: string) => {
      socket.join(`order:${orderId}`);
      logger.info(`User ${socket.userId} tracking order ${orderId}`);
    });

    // Handle package tracking
    socket.on('track_package', (packageId: string) => {
      socket.join(`package:${packageId}`);
      logger.info(`User ${socket.userId} tracking package ${packageId}`);
    });

    // Handle driver location updates
    socket.on('driver_location_update', (data: { latitude: number; longitude: number; orderId?: string; packageId?: string }) => {
      if (socket.user.role === 'delivery_driver') {
        // Broadcast location to relevant order/package rooms
        if (data.orderId) {
          socket.to(`order:${data.orderId}`).emit('driver_location', {
            driverId: socket.userId,
            location: { latitude: data.latitude, longitude: data.longitude },
            timestamp: new Date()
          });
        }
        
        if (data.packageId) {
          socket.to(`package:${data.packageId}`).emit('driver_location', {
            driverId: socket.userId,
            location: { latitude: data.latitude, longitude: data.longitude },
            timestamp: new Date()
          });
        }
      }
    });

    // Handle supplier status updates
    socket.on('supplier_status_update', (data: { isOpen: boolean; estimatedWaitTime?: number }) => {
      if (socket.user.role === 'supplier') {
        // Broadcast to all connected users
        socket.broadcast.emit('supplier_status_changed', {
          supplierId: socket.user.supplierId,
          isOpen: data.isOpen,
          estimatedWaitTime: data.estimatedWaitTime,
          timestamp: new Date()
        });
      }
    });

    // Handle chat messages
    socket.on('send_message', (data: { orderId?: string; packageId?: string; message: string; type: 'text' | 'image' }) => {
      const room = data.orderId ? `order:${data.orderId}` : `package:${data.packageId}`;
      
      const messageData = {
        senderId: socket.userId,
        senderName: `${socket.user.firstName} ${socket.user.lastName}`,
        senderRole: socket.user.role,
        message: data.message,
        type: data.type,
        timestamp: new Date()
      };
      
      // Send to all users in the room
      io.to(room).emit('new_message', messageData);
      
      logger.info(`Message sent in ${room} by ${socket.userId}`);
    });

    // Handle typing indicators
    socket.on('typing_start', (data: { orderId?: string; packageId?: string }) => {
      const room = data.orderId ? `order:${data.orderId}` : `package:${data.packageId}`;
      socket.to(room).emit('user_typing', {
        userId: socket.userId,
        userName: `${socket.user.firstName} ${socket.user.lastName}`
      });
    });

    socket.on('typing_stop', (data: { orderId?: string; packageId?: string }) => {
      const room = data.orderId ? `order:${data.orderId}` : `package:${data.packageId}`;
      socket.to(room).emit('user_stopped_typing', {
        userId: socket.userId
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`User disconnected: ${socket.userId}, reason: ${reason}`);
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`Socket error for user ${socket.userId}:`, error);
    });
  });

  // Helper functions for emitting events
  io.emitToUser = (userId: string, event: string, data: any) => {
    io.to(`user:${userId}`).emit(event, data);
  };

  io.emitToOrder = (orderId: string, event: string, data: any) => {
    io.to(`order:${orderId}`).emit(event, data);
  };

  io.emitToPackage = (packageId: string, event: string, data: any) => {
    io.to(`package:${packageId}`).emit(event, data);
  };

  io.emitToDrivers = (event: string, data: any) => {
    io.to('drivers').emit(event, data);
  };

  io.emitToSuppliers = (event: string, data: any) => {
    io.to('suppliers').emit(event, data);
  };

  io.emitToAdmins = (event: string, data: any) => {
    io.to('admins').emit(event, data);
  };

  logger.info('🔌 Socket.IO configured successfully');
};

// Extend Socket.IO server interface
declare module 'socket.io' {
  interface Server {
    emitToUser(userId: string, event: string, data: any): void;
    emitToOrder(orderId: string, event: string, data: any): void;
    emitToPackage(packageId: string, event: string, data: any): void;
    emitToDrivers(event: string, data: any): void;
    emitToSuppliers(event: string, data: any): void;
    emitToAdmins(event: string, data: any): void;
  }
}
