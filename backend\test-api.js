const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testAPI() {
  try {
    console.log('🧪 Testing API endpoints...\n');

    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    // Test signup
    console.log('2. Testing signup...');
    const signupData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123',
      dateOfBirth: '1990-01-01',
      gender: 'male',
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA',
        coordinates: {
          latitude: 40.7128,
          longitude: -74.0060
        }
      },
      preferences: {
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        language: 'en',
        currency: 'USD'
      }
    };

    const signupResponse = await axios.post(`${BASE_URL}/api/v1/auth/signup`, signupData);
    console.log('✅ Signup successful:', {
      success: signupResponse.data.success,
      message: signupResponse.data.message,
      userId: signupResponse.data.data.user.id,
      email: signupResponse.data.data.user.email,
      hasToken: !!signupResponse.data.data.token
    });
    console.log('');

    // Test login
    console.log('3. Testing login...');
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const loginResponse = await axios.post(`${BASE_URL}/api/v1/auth/login`, loginData);
    console.log('✅ Login successful:', {
      success: loginResponse.data.success,
      message: loginResponse.data.message,
      userId: loginResponse.data.data.user.id,
      email: loginResponse.data.data.user.email,
      hasToken: !!loginResponse.data.data.token
    });
    console.log('');

    // Test email verification
    console.log('4. Testing email verification...');
    const verifyData = {
      email: '<EMAIL>',
      code: '123456'
    };

    const verifyResponse = await axios.post(`${BASE_URL}/api/v1/auth/verify-email`, verifyData);
    console.log('✅ Email verification:', verifyResponse.data);
    console.log('');

    // Test get users
    console.log('5. Testing get users...');
    const usersResponse = await axios.get(`${BASE_URL}/api/v1/auth/users`);
    console.log('✅ Users retrieved:', {
      success: usersResponse.data.success,
      userCount: usersResponse.data.data.length,
      users: usersResponse.data.data.map(u => ({ id: u.id, email: u.email, firstName: u.firstName }))
    });
    console.log('');

    console.log('🎉 All tests passed! Backend is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAPI();
