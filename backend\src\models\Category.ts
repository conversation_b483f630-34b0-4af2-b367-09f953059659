import mongoose, { Document, Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export interface ICategory extends Document {
  // Basic Information
  name: string;
  slug: string;
  description?: string;
  
  // Hierarchy
  parent?: mongoose.Types.ObjectId;
  level: number;
  path: string; // e.g., "restaurants/fast-food/burgers"
  
  // Media
  icon?: string;
  image?: string;
  banner?: string;
  
  // Display
  displayOrder: number;
  isActive: boolean;
  isFeatured: boolean;
  
  // SEO
  metaTitle?: string;
  metaDescription?: string;
  keywords?: string[];
  
  // Supplier Types
  supplierTypes: ('restaurants' | 'clothings' | 'supermarkets' | 'pharmacies' | 'electronics' | 'other')[];
  
  // Statistics
  productCount: number;
  supplierCount: number;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Virtual fields
  children?: ICategory[];
  
  // Methods
  getFullPath(): string;
  getAncestors(): Promise<ICategory[]>;
  getDescendants(): Promise<ICategory[]>;
  updateCounts(): Promise<void>;
}

const CategorySchema = new Schema<ICategory>({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true,
    minlength: [2, 'Category name must be at least 2 characters'],
    maxlength: [100, 'Category name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  level: {
    type: Number,
    required: true,
    min: 0,
    max: 5,
    default: 0
  },
  path: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    trim: true
  },
  image: {
    type: String,
    trim: true
  },
  banner: {
    type: String,
    trim: true
  },
  displayOrder: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  metaTitle: {
    type: String,
    trim: true,
    maxlength: [60, 'Meta title cannot exceed 60 characters']
  },
  metaDescription: {
    type: String,
    trim: true,
    maxlength: [160, 'Meta description cannot exceed 160 characters']
  },
  keywords: [{
    type: String,
    trim: true
  }],
  supplierTypes: [{
    type: String,
    enum: ['restaurants', 'clothings', 'supermarkets', 'pharmacies', 'electronics', 'other']
  }],
  productCount: {
    type: Number,
    default: 0,
    min: 0
  },
  supplierCount: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
CategorySchema.index({ slug: 1 });
CategorySchema.index({ parent: 1, displayOrder: 1 });
CategorySchema.index({ level: 1, isActive: 1 });
CategorySchema.index({ isFeatured: -1, displayOrder: 1 });
CategorySchema.index({ supplierTypes: 1 });
CategorySchema.index({ path: 1 });

// Virtual for children
CategorySchema.virtual('children', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parent'
});

// Generate slug from name if not provided
CategorySchema.pre('save', function(next) {
  if (!this.slug && this.name) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
  next();
});

// Update path and level based on parent
CategorySchema.pre('save', async function(next) {
  if (this.isModified('parent') || this.isNew) {
    if (this.parent) {
      const parent = await mongoose.model('Category').findById(this.parent);
      if (parent) {
        this.level = parent.level + 1;
        this.path = `${parent.path}/${this.slug}`;
      } else {
        return next(new Error('Parent category not found'));
      }
    } else {
      this.level = 0;
      this.path = this.slug;
    }
  }
  next();
});

// Validate maximum depth
CategorySchema.pre('save', function(next) {
  if (this.level > 5) {
    return next(new Error('Category hierarchy cannot exceed 5 levels'));
  }
  next();
});

// Prevent circular references
CategorySchema.pre('save', async function(next) {
  if (this.parent && this.parent.toString() === this._id.toString()) {
    return next(new Error('Category cannot be its own parent'));
  }
  
  // Check if setting this parent would create a circular reference
  if (this.parent) {
    const ancestors = await this.getAncestors();
    const ancestorIds = ancestors.map(a => a._id.toString());
    if (ancestorIds.includes(this._id.toString())) {
      return next(new Error('Circular reference detected in category hierarchy'));
    }
  }
  
  next();
});

// Instance methods
CategorySchema.methods.getFullPath = function(): string {
  return this.path;
};

CategorySchema.methods.getAncestors = async function(): Promise<ICategory[]> {
  const ancestors: ICategory[] = [];
  let current = this;
  
  while (current.parent) {
    const parent = await mongoose.model('Category').findById(current.parent);
    if (!parent) break;
    ancestors.unshift(parent);
    current = parent;
  }
  
  return ancestors;
};

CategorySchema.methods.getDescendants = async function(): Promise<ICategory[]> {
  const descendants = await mongoose.model('Category').find({
    path: { $regex: `^${this.path}/` }
  }).sort({ path: 1 });
  
  return descendants;
};

CategorySchema.methods.updateCounts = async function(): Promise<void> {
  // Update product count
  const Product = mongoose.model('Product');
  this.productCount = await Product.countDocuments({
    category: this.name,
    isActive: true
  });
  
  // Update supplier count
  const Supplier = mongoose.model('Supplier');
  this.supplierCount = await Supplier.countDocuments({
    category: this.supplierTypes.length > 0 ? { $in: this.supplierTypes } : this.name,
    isActive: true,
    isVerified: true
  });
  
  await this.save();
};

// Static methods
CategorySchema.statics.getTree = async function(parentId?: mongoose.Types.ObjectId) {
  const categories = await this.find({
    parent: parentId || null,
    isActive: true
  })
  .sort({ displayOrder: 1, name: 1 })
  .populate('children');
  
  return categories;
};

CategorySchema.statics.getFeatured = async function(limit: number = 10) {
  return this.find({
    isFeatured: true,
    isActive: true
  })
  .sort({ displayOrder: 1 })
  .limit(limit);
};

CategorySchema.statics.search = async function(query: string, supplierType?: string) {
  const searchCriteria: any = {
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { keywords: { $in: [new RegExp(query, 'i')] } }
    ],
    isActive: true
  };
  
  if (supplierType) {
    searchCriteria.supplierTypes = supplierType;
  }
  
  return this.find(searchCriteria)
    .sort({ productCount: -1, name: 1 })
    .limit(20);
};

// Add pagination plugin
CategorySchema.plugin(mongoosePaginate);

export const Category = mongoose.model<ICategory>('Category', CategorySchema);
