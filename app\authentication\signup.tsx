import { Stack } from 'expo-router';
import { Container } from '~/components/Container';
import { MultiStepSignup } from '~/components/authentication-components/MultiStepSignup';

export default function Signup() {
  return (
    <>
      <Stack.Screen options={{ title: 'Create Account', headerShown: false }}/>
      <Container flex={1} alignSelf='center' style={{ width: '100%' }}>
        <MultiStepSignup />
      </Container>
    </>
  );
}
