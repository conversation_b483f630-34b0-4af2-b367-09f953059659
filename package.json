{"name": "wasel", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start --dev-client", "ios": "expo run:ios", "android": "expo run:android", "build:dev": "eas build --profile development", "build:preview": "eas build --profile preview", "build:prod": "eas build --profile production", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/native": "^7.0.3", "@rnmapbox/maps": "^10.1.39", "@tamagui/animations-react-native": "^1.125.8", "@tamagui/font-inter": "^1.125.8", "@tamagui/lucide-icons": "^1.127.0", "@tamagui/react-native-media-driver": "^1.125.8", "@tamagui/shorthands": "^1.125.8", "@tamagui/themes": "^1.125.8", "axios": "^1.10.0", "expo": "^53.0.13", "expo-constants": "~17.1.4", "expo-dev-client": "~5.2.0", "expo-dev-launcher": "^5.0.17", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-location": "~18.1.5", "expo-router": "~5.1.0", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "firebase": "^10.5.2", "lottie-react-native": "7.2.2", "moti": "^0.30.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tamagui": "^1.125.8", "zustand": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@tamagui/babel-plugin": "^1.125.8", "@types/react": "~19.0.10", "ajv": "^8.12.0", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "typescript": "~5.8.3"}, "private": true}