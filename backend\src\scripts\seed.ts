import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import { User } from '../models/User';
import { connectDB } from '../config/database';

const seedUsers = async () => {
  try {
    await connectDB();

    // Check if default user already exists
    const defaultUser = await User.findOne({ email: '<EMAIL>' });
    if (defaultUser) {
      console.log('Default user already exists');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
      return;
    }

    // Create default admin user
    const defaultUserData = {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+1234567890',
      password: await bcrypt.hash('admin123', 12),
      isEmailVerified: true,
      isPhoneVerified: true,
      role: 'customer',
      dateOfBirth: new Date('1990-01-01'),
      gender: 'other',
      address: {
        street: '123 Admin Street',
        city: 'Admin City',
        state: 'AC',
        zipCode: '12345',
        country: 'USA',
        coordinates: {
          latitude: 40.7128,
          longitude: -74.0060
        }
      },
      preferences: {
        language: 'en',
        currency: 'USD',
        notifications: {
          email: true,
          push: true,
          sms: false
        }
      }
    };

    await User.create(defaultUserData);
    console.log('✅ Default user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');

    // Create additional sample users
    const sampleUsers = [
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567891',
        password: await bcrypt.hash('password123', 12),
        isEmailVerified: true,
        isPhoneVerified: true,
        role: 'customer',
        dateOfBirth: new Date('1985-05-15'),
        gender: 'male',
        address: {
          street: '456 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA',
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          }
        },
        preferences: {
          language: 'en',
          currency: 'USD',
          notifications: {
            email: true,
            push: true,
            sms: false
          }
        }
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567892',
        password: await bcrypt.hash('password123', 12),
        isEmailVerified: true,
        isPhoneVerified: true,
        role: 'customer',
        dateOfBirth: new Date('1992-08-20'),
        gender: 'female',
        address: {
          street: '789 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'USA',
          coordinates: {
            latitude: 34.0522,
            longitude: -118.2437
          }
        },
        preferences: {
          language: 'en',
          currency: 'USD',
          notifications: {
            email: true,
            push: true,
            sms: true
          }
        }
      }
    ];

    await User.insertMany(sampleUsers);
    console.log('✅ Sample users created successfully');

  } catch (error) {
    console.error('❌ Error seeding users:', error);
  } finally {
    await mongoose.connection.close();
  }
};

seedUsers();
