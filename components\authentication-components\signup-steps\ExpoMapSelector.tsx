import React, { useState } from 'react';
import { Modal, Dimensions } from 'react-native';
import { YStack, XStack, H5, Text, Button } from 'tamagui';
import MapView, { Marker, Region } from 'react-native-maps';
import * as Location from 'expo-location';
import { Address } from '../../../services/api';

interface ExpoMapSelectorProps {
  onAddressSelect: (address: Omit<Address, '_id'>) => void;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

export const ExpoMapSelector: React.FC<ExpoMapSelectorProps> = ({
  onAddressSelect,
  onClose,
}) => {
  const [selectedCoordinates, setSelectedCoordinates] = useState<{latitude: number, longitude: number} | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [region, setRegion] = useState<Region>({
    latitude: 31.7683, // Jerusalem default
    longitude: 35.2137,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Handle map press
  const onMapPress = async (event: any) => {
    const coordinates = event.nativeEvent.coordinate;
    setSelectedCoordinates(coordinates);
    
    // Reverse geocode to get address
    await reverseGeocode(coordinates.latitude, coordinates.longitude);
  };

  // Reverse geocode coordinates to address
  const reverseGeocode = async (latitude: number, longitude: number) => {
    try {
      setIsLoading(true);
      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (result.length > 0) {
        const location = result[0];
        const addressString = [
          location.streetNumber,
          location.street,
          location.city,
          location.region,
          location.country
        ].filter(Boolean).join(', ');
        
        setSelectedAddress(addressString);
      }
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      setSelectedAddress('Address not found');
    } finally {
      setIsLoading(false);
    }
  };

  // Get current location
  const getCurrentLocation = async () => {
    try {
      setIsLoading(true);
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        alert('Permission to access location was denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const newRegion = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      
      setRegion(newRegion);
      setSelectedCoordinates({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
      
      await reverseGeocode(location.coords.latitude, location.coords.longitude);
    } catch (error) {
      console.error('Error getting current location:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Confirm address selection
  const confirmSelection = () => {
    if (!selectedCoordinates || !selectedAddress) {
      alert('Please select a location on the map');
      return;
    }

    const address: Omit<Address, '_id'> = {
      street: selectedAddress,
      city: 'Selected City',
      state: 'Selected State',
      zipCode: '00000',
      country: 'Selected Country',
      coordinates: {
        latitude: selectedCoordinates.latitude,
        longitude: selectedCoordinates.longitude,
      },
    };

    onAddressSelect(address);
    onClose();
  };

  return (
    <Modal visible={true} animationType="slide" presentationStyle="pageSheet">
      <YStack flex={1} backgroundColor="$background">
        {/* Header */}
        <YStack padding="$4" backgroundColor="$purple10">
          <XStack justifyContent="space-between" alignItems="center">
            <H5 color="white">Select Address</H5>
            <Button
              size="$3"
              circular
              backgroundColor="$purple8"
              onPress={onClose}
            >
              ✕
            </Button>
          </XStack>
        </YStack>

        {/* Controls */}
        <YStack padding="$3" gap="$2">
          <XStack gap="$2">
            <Button
              flex={1}
              size="$3"
              backgroundColor="$purple10"
              color="white"
              onPress={getCurrentLocation}
              disabled={isLoading}
            >
              📍 Current Location
            </Button>
            <Button
              flex={1}
              size="$3"
              backgroundColor="$green10"
              color="white"
              onPress={confirmSelection}
              disabled={!selectedCoordinates || isLoading}
            >
              ✓ Confirm
            </Button>
          </XStack>

          {selectedAddress && (
            <YStack padding="$2" backgroundColor="$gray2" borderRadius="$3">
              <Text fontSize="$3" fontWeight="600">Selected Address:</Text>
              <Text fontSize="$2" color="$gray11">{selectedAddress}</Text>
            </YStack>
          )}
        </YStack>

        {/* Map */}
        <YStack flex={1}>
          <MapView
            style={{ flex: 1 }}
            region={region}
            onPress={onMapPress}
            showsUserLocation={true}
            showsMyLocationButton={false}
          >
            {selectedCoordinates && (
              <Marker
                coordinate={selectedCoordinates}
                title="Selected Location"
                description={selectedAddress}
              />
            )}
          </MapView>
        </YStack>

        {isLoading && (
          <YStack
            position="absolute"
            top={0}
            bottom={0}
            left={0}
            right={0}
            backgroundColor="rgba(0,0,0,0.3)"
            alignItems="center"
            justifyContent="center"
          >
            <YStack
              backgroundColor="white"
              padding="$4"
              borderRadius="$4"
              alignItems="center"
              gap="$2"
            >
              <Text>Loading...</Text>
            </YStack>
          </YStack>
        )}
      </YStack>
    </Modal>
  );
};
