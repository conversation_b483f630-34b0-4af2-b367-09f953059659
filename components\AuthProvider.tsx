import React, { useEffect, useState } from 'react';
import { YStack, Text, Spinner } from 'tamagui';
import { useAuthStore } from '../store/store';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { loadAuthData, isLoading } = useAuthStore();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Add a delay to ensure storage is properly initialized
        await new Promise(resolve => setTimeout(resolve, 200));
        await loadAuthData();
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Try one more time after a longer delay
        try {
          await new Promise(resolve => setTimeout(resolve, 500));
          await loadAuthData();
        } catch (retryError) {
          console.error('Retry failed:', retryError);
        }
      } finally {
        setIsInitializing(false);
      }
    };

    initializeAuth();
  }, [loadAuthData]);

  if (isInitializing || isLoading) {
    return (
      <YStack flex={1} alignItems="center" justifyContent="center" backgroundColor="$purple10">
        <YStack alignItems="center" gap="$4">
          <Spinner size="large" color="white" />
          <Text color="white" fontSize="$4" fontWeight="600">
            Loading Wasel...
          </Text>
        </YStack>
      </YStack>
    );
  }

  return <>{children}</>;
};
