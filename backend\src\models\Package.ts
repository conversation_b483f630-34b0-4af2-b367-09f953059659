import mongoose, { Document, Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export interface IPackageLocation {
  address: string;
  building?: string;
  floor?: string;
  apartment?: string;
  city: string;
  area: string;
  landmark?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  contactName: string;
  contactPhone: string;
  instructions?: string;
}

export interface IPackageItem {
  description: string;
  quantity: number;
  weight?: number;
  value?: number;
  category: 'documents' | 'electronics' | 'clothing' | 'food' | 'fragile' | 'other';
  isFragile: boolean;
  specialHandling?: string;
}

export interface IPackage extends Document {
  // Basic Information
  packageNumber: string;
  sender: mongoose.Types.ObjectId;
  
  // Package Details
  items: IPackageItem[];
  totalWeight?: number;
  totalValue?: number;
  packageType: 'envelope' | 'small_box' | 'medium_box' | 'large_box' | 'custom';
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: 'cm' | 'inch';
  };
  
  // Pickup Information
  pickupLocation: IPackageLocation;
  pickupTimeSlot?: {
    date: Date;
    startTime: string;
    endTime: string;
  };
  pickupInstructions?: string;
  actualPickupTime?: Date;
  
  // Delivery Information
  deliveryLocation: IPackageLocation;
  deliveryTimeSlot?: {
    date: Date;
    startTime: string;
    endTime: string;
  };
  deliveryInstructions?: string;
  actualDeliveryTime?: Date;
  
  // Service Options
  serviceType: 'standard' | 'express' | 'same_day' | 'scheduled';
  isInsured: boolean;
  insuranceValue?: number;
  requiresSignature: boolean;
  cashOnDelivery?: {
    amount: number;
    currency: string;
  };
  
  // Pricing
  baseFee: number;
  distanceFee: number;
  weightFee: number;
  serviceFee: number;
  insuranceFee: number;
  total: number;
  
  // Status & Tracking
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'failed_delivery' | 'cancelled' | 'returned';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  
  // Driver Information
  driver?: mongoose.Types.ObjectId;
  driverAssignedAt?: Date;
  
  // Tracking
  trackingHistory: Array<{
    status: string;
    location?: {
      address: string;
      coordinates: {
        latitude: number;
        longitude: number;
      };
    };
    timestamp: Date;
    note?: string;
    updatedBy?: mongoose.Types.ObjectId;
  }>;
  
  // Delivery Attempts
  deliveryAttempts: Array<{
    attemptNumber: number;
    timestamp: Date;
    status: 'successful' | 'failed';
    reason?: string;
    nextAttemptDate?: Date;
  }>;
  
  // Photos & Proof
  pickupPhotos?: string[];
  deliveryPhotos?: string[];
  signatureImage?: string;
  
  // Payment Information
  paymentMethod: 'cash' | 'card' | 'digital_wallet';
  paymentInfo?: {
    transactionId?: string;
    paidAt?: Date;
    refundedAt?: Date;
    refundAmount?: number;
  };
  
  // Estimated Times
  estimatedPickupTime?: Date;
  estimatedDeliveryTime?: Date;
  
  // Special Instructions
  senderNotes?: string;
  driverNotes?: string;
  
  // Cancellation
  cancellationReason?: string;
  cancelledBy?: mongoose.Types.ObjectId;
  cancelledAt?: Date;
  
  // Rating & Review
  rating?: {
    service: number;
    driver: number;
    overall: number;
    comment?: string;
    ratedAt: Date;
  };
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Methods
  calculateTotal(): number;
  updateStatus(status: string, location?: any, note?: string, updatedBy?: mongoose.Types.ObjectId): Promise<void>;
  canBeCancelled(): boolean;
  getEstimatedDeliveryTime(): Date;
  addDeliveryAttempt(status: 'successful' | 'failed', reason?: string): Promise<void>;
}

const PackageLocationSchema = new Schema<IPackageLocation>({
  address: {
    type: String,
    required: true,
    trim: true
  },
  building: String,
  floor: String,
  apartment: String,
  city: {
    type: String,
    required: true,
    trim: true
  },
  area: {
    type: String,
    required: true,
    trim: true
  },
  landmark: String,
  coordinates: {
    latitude: {
      type: Number,
      required: true,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      required: true,
      min: -180,
      max: 180
    }
  },
  contactName: {
    type: String,
    required: true,
    trim: true
  },
  contactPhone: {
    type: String,
    required: true,
    trim: true
  },
  instructions: {
    type: String,
    maxlength: 500
  }
}, { _id: false });

const PackageItemSchema = new Schema<IPackageItem>({
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  weight: {
    type: Number,
    min: 0
  },
  value: {
    type: Number,
    min: 0
  },
  category: {
    type: String,
    enum: ['documents', 'electronics', 'clothing', 'food', 'fragile', 'other'],
    required: true
  },
  isFragile: {
    type: Boolean,
    default: false
  },
  specialHandling: {
    type: String,
    maxlength: 200
  }
}, { _id: false });

const PackageSchema = new Schema<IPackage>({
  packageNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  sender: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: {
    type: [PackageItemSchema],
    required: true,
    validate: {
      validator: function(items: IPackageItem[]) {
        return items.length > 0;
      },
      message: 'Package must have at least one item'
    }
  },
  totalWeight: {
    type: Number,
    min: 0
  },
  totalValue: {
    type: Number,
    min: 0
  },
  packageType: {
    type: String,
    enum: ['envelope', 'small_box', 'medium_box', 'large_box', 'custom'],
    required: true
  },
  dimensions: {
    length: { type: Number, min: 0 },
    width: { type: Number, min: 0 },
    height: { type: Number, min: 0 },
    unit: {
      type: String,
      enum: ['cm', 'inch'],
      default: 'cm'
    }
  },
  pickupLocation: {
    type: PackageLocationSchema,
    required: true
  },
  pickupTimeSlot: {
    date: Date,
    startTime: String,
    endTime: String
  },
  pickupInstructions: {
    type: String,
    maxlength: 500
  },
  actualPickupTime: Date,
  deliveryLocation: {
    type: PackageLocationSchema,
    required: true
  },
  deliveryTimeSlot: {
    date: Date,
    startTime: String,
    endTime: String
  },
  deliveryInstructions: {
    type: String,
    maxlength: 500
  },
  actualDeliveryTime: Date,
  serviceType: {
    type: String,
    enum: ['standard', 'express', 'same_day', 'scheduled'],
    default: 'standard'
  },
  isInsured: {
    type: Boolean,
    default: false
  },
  insuranceValue: {
    type: Number,
    min: 0
  },
  requiresSignature: {
    type: Boolean,
    default: false
  },
  cashOnDelivery: {
    amount: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD'
    }
  },
  baseFee: {
    type: Number,
    required: true,
    min: 0
  },
  distanceFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  weightFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  serviceFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  insuranceFee: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'cancelled', 'returned'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  driver: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  driverAssignedAt: Date,
  trackingHistory: [{
    status: {
      type: String,
      required: true
    },
    location: {
      address: String,
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  deliveryAttempts: [{
    attemptNumber: {
      type: Number,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['successful', 'failed'],
      required: true
    },
    reason: String,
    nextAttemptDate: Date
  }],
  pickupPhotos: [String],
  deliveryPhotos: [String],
  signatureImage: String,
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'digital_wallet'],
    required: true
  },
  paymentInfo: {
    transactionId: String,
    paidAt: Date,
    refundedAt: Date,
    refundAmount: {
      type: Number,
      min: 0
    }
  },
  estimatedPickupTime: Date,
  estimatedDeliveryTime: Date,
  senderNotes: {
    type: String,
    maxlength: 1000
  },
  driverNotes: {
    type: String,
    maxlength: 1000
  },
  cancellationReason: String,
  cancelledBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  cancelledAt: Date,
  rating: {
    service: {
      type: Number,
      min: 1,
      max: 5
    },
    driver: {
      type: Number,
      min: 1,
      max: 5
    },
    overall: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: 1000
    },
    ratedAt: Date
  }
}, {
  timestamps: true
});

// Indexes
PackageSchema.index({ packageNumber: 1 });
PackageSchema.index({ sender: 1, createdAt: -1 });
PackageSchema.index({ driver: 1, status: 1 });
PackageSchema.index({ status: 1, createdAt: -1 });
PackageSchema.index({ 'pickupLocation.coordinates': '2dsphere' });
PackageSchema.index({ 'deliveryLocation.coordinates': '2dsphere' });

// Generate package number before saving
PackageSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await mongoose.model('Package').countDocuments();
    this.packageNumber = `PKG${String(count + 1).padStart(6, '0')}`;
    
    // Add initial status to tracking history
    this.trackingHistory.push({
      status: this.status,
      timestamp: new Date(),
      note: 'Package created'
    });
  }
  next();
});

// Instance methods
PackageSchema.methods.calculateTotal = function(): number {
  return this.baseFee + this.distanceFee + this.weightFee + this.serviceFee + this.insuranceFee;
};

PackageSchema.methods.updateStatus = async function(
  status: string,
  location?: any,
  note?: string,
  updatedBy?: mongoose.Types.ObjectId
): Promise<void> {
  this.status = status;
  this.trackingHistory.push({
    status,
    location,
    timestamp: new Date(),
    note,
    updatedBy
  });
  
  // Update specific timestamps based on status
  if (status === 'picked_up') {
    this.actualPickupTime = new Date();
  } else if (status === 'delivered') {
    this.actualDeliveryTime = new Date();
  } else if (status === 'cancelled') {
    this.cancelledAt = new Date();
  }
  
  await this.save();
};

PackageSchema.methods.canBeCancelled = function(): boolean {
  const cancellableStatuses = ['pending', 'confirmed'];
  return cancellableStatuses.includes(this.status);
};

PackageSchema.methods.getEstimatedDeliveryTime = function(): Date {
  if (this.estimatedDeliveryTime) {
    return this.estimatedDeliveryTime;
  }
  
  // Calculate based on service type
  const now = new Date();
  let hoursToAdd = 24; // standard delivery
  
  switch (this.serviceType) {
    case 'same_day':
      hoursToAdd = 8;
      break;
    case 'express':
      hoursToAdd = 12;
      break;
    case 'standard':
      hoursToAdd = 24;
      break;
    case 'scheduled':
      return this.deliveryTimeSlot?.date || new Date(now.getTime() + 24 * 60 * 60 * 1000);
  }
  
  return new Date(now.getTime() + hoursToAdd * 60 * 60 * 1000);
};

PackageSchema.methods.addDeliveryAttempt = async function(
  status: 'successful' | 'failed',
  reason?: string
): Promise<void> {
  const attemptNumber = this.deliveryAttempts.length + 1;
  
  this.deliveryAttempts.push({
    attemptNumber,
    timestamp: new Date(),
    status,
    reason,
    nextAttemptDate: status === 'failed' ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined
  });
  
  if (status === 'failed') {
    this.status = 'failed_delivery';
  } else {
    this.status = 'delivered';
    this.actualDeliveryTime = new Date();
  }
  
  await this.save();
};

// Add pagination plugin
PackageSchema.plugin(mongoosePaginate);

export const Package = mongoose.model<IPackage>('Package', PackageSchema);
