import React, { useEffect } from 'react';
import { YS<PERSON>ck, XStack, H5, Text, Button, Label, Switch } from 'tamagui';
import { useForm, FormProvider } from 'react-hook-form';
import { useSignupStore, validateStep4 } from '../../../store/signupStore';
import { SignupStep4Data } from '../../../services/api';

export const SignupStep4 = () => {
  const { step4Data, updateStep4Data, setStepValid } = useSignupStore();

  const methods = useForm<SignupStep4Data>({
    defaultValues: step4Data,
    mode: 'onChange',
  });

  const { watch, setValue, formState: { isValid } } = methods;
  const watchedValues = watch();

  // Update store when form values change
  useEffect(() => {
    updateStep4Data(watchedValues);
    const isStepValid = validateStep4(watchedValues);
    setStepValid(4, isStepValid);
  }, [watchedValues, updateStep4Data, setStepValid]);

  // Handle language selection
  const handleLanguageSelect = (language: 'en' | 'ar') => {
    setValue('language', language);
  };

  // Handle currency selection
  const handleCurrencySelect = (currency: 'USD' | 'ILS' | 'JOD') => {
    setValue('currency', currency);
  };

  // Handle notification toggle
  const handleNotificationToggle = (key: keyof SignupStep4Data['notifications'], value: boolean) => {
    setValue(`notifications.${key}`, value);
  };

  return (
    <FormProvider {...methods}>
      <YStack flex={1} gap="$4">
        {/* Header */}
        <YStack gap="$2" paddingBottom="$3">
          <H5 color="$purple10">Preferences</H5>
          <Text color="$gray10" fontSize="$3">
            Customize your experience
          </Text>
        </YStack>

        {/* Language Selection */}
        <YStack gap="$3">
          <Label color="$gray11" fontSize="$3" fontWeight="600">
            Preferred Language
          </Label>
          <XStack gap="$2">
            <Button
              flex={1}
              variant="outlined"
              backgroundColor={watchedValues.language === 'en' ? '$purple3' : '$gray2'}
              borderColor={watchedValues.language === 'en' ? '$purple8' : '$gray7'}
              onPress={() => handleLanguageSelect('en')}
            >
              <Text color={watchedValues.language === 'en' ? '$purple11' : '$gray11'}>
                🇺🇸 English
              </Text>
            </Button>
            <Button
              flex={1}
              variant="outlined"
              backgroundColor={watchedValues.language === 'ar' ? '$purple3' : '$gray2'}
              borderColor={watchedValues.language === 'ar' ? '$purple8' : '$gray7'}
              onPress={() => handleLanguageSelect('ar')}
            >
              <Text color={watchedValues.language === 'ar' ? '$purple11' : '$gray11'}>
                🇸🇦 العربية
              </Text>
            </Button>
          </XStack>
        </YStack>

        {/* Currency Selection */}
        <YStack gap="$3">
          <Label color="$gray11" fontSize="$3" fontWeight="600">
            Preferred Currency
          </Label>
          <XStack gap="$2">
            <Button
              flex={1}
              variant="outlined"
              backgroundColor={watchedValues.currency === 'USD' ? '$purple3' : '$gray2'}
              borderColor={watchedValues.currency === 'USD' ? '$purple8' : '$gray7'}
              onPress={() => handleCurrencySelect('USD')}
            >
              <Text color={watchedValues.currency === 'USD' ? '$purple11' : '$gray11'}>
                $ USD
              </Text>
            </Button>
            <Button
              flex={1}
              variant="outlined"
              backgroundColor={watchedValues.currency === 'ILS' ? '$purple3' : '$gray2'}
              borderColor={watchedValues.currency === 'ILS' ? '$purple8' : '$gray7'}
              onPress={() => handleCurrencySelect('ILS')}
            >
              <Text color={watchedValues.currency === 'ILS' ? '$purple11' : '$gray11'}>
                ₪ ILS
              </Text>
            </Button>
            <Button
              flex={1}
              variant="outlined"
              backgroundColor={watchedValues.currency === 'JOD' ? '$purple3' : '$gray2'}
              borderColor={watchedValues.currency === 'JOD' ? '$purple8' : '$gray7'}
              onPress={() => handleCurrencySelect('JOD')}
            >
              <Text color={watchedValues.currency === 'JOD' ? '$purple11' : '$gray11'}>
                د.أ JOD
              </Text>
            </Button>
          </XStack>
        </YStack>

        {/* Notification Preferences */}
        <YStack gap="$3">
          <Label color="$gray11" fontSize="$3" fontWeight="600">
            Notification Preferences
          </Label>
          
          <YStack gap="$3" padding="$3" backgroundColor="$gray2" borderRadius="$4">
            {/* Email Notifications */}
            <XStack justifyContent="space-between" alignItems="center">
              <YStack flex={1}>
                <Text color="$gray11" fontSize="$3" fontWeight="500">
                  Email Notifications
                </Text>
                <Text color="$gray9" fontSize="$2">
                  Receive updates via email
                </Text>
              </YStack>
              <Switch
                checked={watchedValues.notifications?.email ?? true}
                onCheckedChange={(value) => handleNotificationToggle('email', value)}
              />
            </XStack>

            {/* SMS Notifications */}
            <XStack justifyContent="space-between" alignItems="center">
              <YStack flex={1}>
                <Text color="$gray11" fontSize="$3" fontWeight="500">
                  SMS Notifications
                </Text>
                <Text color="$gray9" fontSize="$2">
                  Receive updates via SMS
                </Text>
              </YStack>
              <Switch
                checked={watchedValues.notifications?.sms ?? true}
                onCheckedChange={(value) => handleNotificationToggle('sms', value)}
              />
            </XStack>

            {/* Push Notifications */}
            <XStack justifyContent="space-between" alignItems="center">
              <YStack flex={1}>
                <Text color="$gray11" fontSize="$3" fontWeight="500">
                  Push Notifications
                </Text>
                <Text color="$gray9" fontSize="$2">
                  Receive app notifications
                </Text>
              </YStack>
              <Switch
                checked={watchedValues.notifications?.push ?? true}
                onCheckedChange={(value) => handleNotificationToggle('push', value)}
              />
            </XStack>

            {/* Order Updates */}
            <XStack justifyContent="space-between" alignItems="center">
              <YStack flex={1}>
                <Text color="$gray11" fontSize="$3" fontWeight="500">
                  Order Updates
                </Text>
                <Text color="$gray9" fontSize="$2">
                  Get notified about order status
                </Text>
              </YStack>
              <Switch
                checked={watchedValues.notifications?.orderUpdates ?? true}
                onCheckedChange={(value) => handleNotificationToggle('orderUpdates', value)}
              />
            </XStack>

            {/* Promotions */}
            <XStack justifyContent="space-between" alignItems="center">
              <YStack flex={1}>
                <Text color="$gray11" fontSize="$3" fontWeight="500">
                  Promotions & Offers
                </Text>
                <Text color="$gray9" fontSize="$2">
                  Receive special offers and deals
                </Text>
              </YStack>
              <Switch
                checked={watchedValues.notifications?.promotions ?? false}
                onCheckedChange={(value) => handleNotificationToggle('promotions', value)}
              />
            </XStack>

            {/* Newsletter */}
            <XStack justifyContent="space-between" alignItems="center">
              <YStack flex={1}>
                <Text color="$gray11" fontSize="$3" fontWeight="500">
                  Newsletter
                </Text>
                <Text color="$gray9" fontSize="$2">
                  Receive our weekly newsletter
                </Text>
              </YStack>
              <Switch
                checked={watchedValues.notifications?.newsletter ?? false}
                onCheckedChange={(value) => handleNotificationToggle('newsletter', value)}
              />
            </XStack>
          </YStack>
        </YStack>

        {/* Info Text */}
        <YStack paddingTop="$2">
          <Text color="$gray9" fontSize="$2" textAlign="center">
            You can change these preferences anytime in your account settings.
          </Text>
        </YStack>
      </YStack>
    </FormProvider>
  );
};
