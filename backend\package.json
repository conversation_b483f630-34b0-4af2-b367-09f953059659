{"name": "wasel-backend", "version": "1.0.0", "description": "Professional backend for Wasel delivery application", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "socket.io": "^4.7.4", "stripe": "^14.9.0", "nodemailer": "^6.9.7", "redis": "^4.6.11", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/compression": "^1.7.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "prisma": "^5.7.1"}, "keywords": ["delivery", "backend", "api", "nodejs", "express", "typescript", "prisma", "postgresql"], "author": "Wasel Team", "license": "MIT"}