import React, { useState, useRef } from 'react';
import { Modal, Dimensions } from 'react-native';
import { YStack, XStack, H5, Text, Button } from 'tamagui';
import MapboxGL from '@rnmapbox/maps';
import * as Location from 'expo-location';
import { Address } from '../../../services/api';

interface AddressMapSelectorProps {
  onAddressSelect: (address: Omit<Address, '_id'>) => void;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

export const AddressMapSelector: React.FC<AddressMapSelectorProps> = ({
  onAddressSelect,
  onClose,
}) => {
  const [selectedCoordinates, setSelectedCoordinates] = useState<[number, number] | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const mapRef = useRef<MapboxGL.MapView>(null);

  // Handle map press
  const onMapPress = async (feature: any) => {
    const coordinates = feature.geometry.coordinates as [number, longitude];
    setSelectedCoordinates(coordinates);
    
    // Reverse geocode to get address
    await reverseGeocode(coordinates[1], coordinates[0]);
  };

  // Reverse geocode coordinates to address
  const reverseGeocode = async (latitude: number, longitude: number) => {
    setIsLoading(true);
    try {
      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (result && result.length > 0) {
        const location = result[0];
        const addressString = [
          location.streetNumber,
          location.street,
          location.district,
          location.city,
          location.region,
        ]
          .filter(Boolean)
          .join(', ');

        setSelectedAddress(addressString);
      }
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      setSelectedAddress('Unable to get address for this location');
    } finally {
      setIsLoading(false);
    }
  };

  // Get current location
  const getCurrentLocation = async () => {
    setIsLoading(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        alert('Permission to access location was denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const coordinates: [number, number] = [
        location.coords.longitude,
        location.coords.latitude,
      ];

      setSelectedCoordinates(coordinates);
      
      // Center map on current location
      if (mapRef.current) {
        mapRef.current.setCamera({
          centerCoordinate: coordinates,
          zoomLevel: 15,
          animationDuration: 1000,
        });
      }

      await reverseGeocode(location.coords.latitude, location.coords.longitude);
    } catch (error) {
      console.error('Error getting current location:', error);
      alert('Unable to get current location');
    } finally {
      setIsLoading(false);
    }
  };

  // Confirm address selection
  const confirmSelection = () => {
    if (!selectedCoordinates || !selectedAddress) {
      alert('Please select a location on the map');
      return;
    }

    // Parse address components (basic parsing)
    const addressParts = selectedAddress.split(', ');
    const street = addressParts.slice(0, 2).join(' ') || 'Unknown Street';
    const area = addressParts[2] || 'Unknown Area';
    const city = addressParts[3] || 'Unknown City';

    const address: Omit<Address, '_id'> = {
      type: 'home',
      street,
      city,
      area,
      coordinates: {
        latitude: selectedCoordinates[1],
        longitude: selectedCoordinates[0],
      },
      isDefault: true,
    };

    onAddressSelect(address);
  };

  return (
    <Modal visible={true} animationType="slide" presentationStyle="fullScreen">
      <YStack flex={1} backgroundColor="white">
        {/* Header */}
        <YStack padding="$4" paddingTop="$6" backgroundColor="$purple10">
          <XStack justifyContent="space-between" alignItems="center">
            <H5 color="white">Select Address</H5>
            <Button
              size="$3"
              circular
              backgroundColor="$purple8"
              color="white"
              onPress={onClose}
            >
              ✕
            </Button>
          </XStack>
          <Text color="$purple2" fontSize="$3" paddingTop="$2">
            Tap on the map to select your address
          </Text>
        </YStack>

        {/* Map */}
        <YStack flex={1}>
          <MapboxGL.MapView
            ref={mapRef}
            style={{ flex: 1 }}
            onPress={onMapPress}
            zoomEnabled={true}
            scrollEnabled={true}
            pitchEnabled={false}
            rotateEnabled={false}
          >
            <MapboxGL.Camera
              zoomLevel={12}
              centerCoordinate={[35.2137, 31.7683]} // Default to Jerusalem
            />

            {selectedCoordinates && (
              <MapboxGL.PointAnnotation
                id="selected-location"
                coordinate={selectedCoordinates}
              >
                <YStack
                  width={30}
                  height={30}
                  backgroundColor="$purple10"
                  borderRadius={15}
                  alignItems="center"
                  justifyContent="center"
                  borderWidth={3}
                  borderColor="white"
                >
                  <Text color="white" fontSize="$1" fontWeight="bold">
                    📍
                  </Text>
                </YStack>
              </MapboxGL.PointAnnotation>
            )}
          </MapboxGL.MapView>
        </YStack>

        {/* Address Info */}
        {selectedAddress && (
          <YStack padding="$4" backgroundColor="$gray2" borderTopWidth={1} borderTopColor="$gray6">
            <Text color="$gray11" fontSize="$3" fontWeight="600" paddingBottom="$2">
              Selected Location:
            </Text>
            <Text color="$gray10" fontSize="$3">
              {isLoading ? 'Getting address...' : selectedAddress}
            </Text>
          </YStack>
        )}

        {/* Action Buttons */}
        <YStack padding="$4" gap="$3" backgroundColor="white">
          <Button
            backgroundColor="$blue10"
            color="white"
            onPress={getCurrentLocation}
            disabled={isLoading}
            icon="location"
          >
            {isLoading ? 'Getting Location...' : 'Use Current Location'}
          </Button>

          <XStack gap="$3">
            <Button
              flex={1}
              variant="outlined"
              onPress={onClose}
            >
              Cancel
            </Button>
            <Button
              flex={2}
              backgroundColor="$purple10"
              color="white"
              onPress={confirmSelection}
              disabled={!selectedCoordinates || isLoading}
            >
              Confirm Address
            </Button>
          </XStack>
        </YStack>
      </YStack>
    </Modal>
  );
};
