import React from 'react';
import { YStack, Text, Button } from 'tamagui';
import { Alert } from 'react-native';

interface MapboxPlaceholderProps {
  title?: string;
  message?: string;
  onPress?: () => void;
}

export const MapboxPlaceholder: React.FC<MapboxPlaceholderProps> = ({
  title = "Map Feature",
  message = "This feature requires a development build to use Mapbox. For now, using Expo's built-in maps.",
  onPress
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      Alert.alert(
        "Map Feature Unavailable",
        "This feature uses Mapbox which requires a development build. Please use 'expo run:ios' or 'expo run:android' to test map features.",
        [{ text: "OK" }]
      );
    }
  };

  return (
    <YStack
      flex={1}
      alignItems="center"
      justifyContent="center"
      padding="$4"
      backgroundColor="$gray2"
      borderRadius="$4"
      borderWidth={2}
      borderColor="$gray6"
      borderStyle="dashed"
    >
      <Text fontSize="$6" marginBottom="$2">🗺️</Text>
      <Text fontSize="$5" fontWeight="bold" textAlign="center" marginBottom="$2">
        {title}
      </Text>
      <Text fontSize="$3" color="$gray11" textAlign="center" marginBottom="$4">
        {message}
      </Text>
      <Button
        size="$3"
        backgroundColor="$blue10"
        color="white"
        onPress={handlePress}
      >
        Learn More
      </Button>
    </YStack>
  );
};
