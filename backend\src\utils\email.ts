import nodemailer from 'nodemailer';
import { logger } from './logger';

interface EmailOptions {
  to: string;
  subject: string;
  template: string;
  data: any;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

// Create transporter
const createTransporter = () => {
  const emailService = process.env.EMAIL_SERVICE || 'gmail';
  
  if (emailService === 'gmail') {
    return nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_APP_PASSWORD // Use app password for Gmail
      }
    });
  }
  
  if (emailService === 'smtp') {
    return nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
      }
    });
  }
  
  // Default to SendGrid
  return nodemailer.createTransporter({
    service: 'SendGrid',
    auth: {
      user: 'apikey',
      pass: process.env.SENDGRID_API_KEY
    }
  });
};

// Email templates
const getEmailTemplate = (template: string, data: any): { html: string; text: string } => {
  switch (template) {
    case 'emailVerification':
      return {
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verify Your Email - Wasel</title>
            <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #007bff; color: white; padding: 20px; text-align: center; }
              .content { padding: 30px 20px; }
              .button { 
                display: inline-block; 
                background: #007bff; 
                color: white; 
                padding: 12px 30px; 
                text-decoration: none; 
                border-radius: 5px; 
                margin: 20px 0; 
              }
              .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>Welcome to Wasel!</h1>
              </div>
              <div class="content">
                <h2>Hi ${data.firstName},</h2>
                <p>Thank you for signing up with Wasel! To complete your registration, please verify your email address by clicking the button below:</p>
                <div style="text-align: center;">
                  <a href="${data.verificationUrl}" class="button">Verify Email Address</a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #007bff;">${data.verificationUrl}</p>
                <p>This verification link will expire in 24 hours for security reasons.</p>
                <p>If you didn't create an account with Wasel, please ignore this email.</p>
                <p>Best regards,<br>The Wasel Team</p>
              </div>
              <div class="footer">
                <p>&copy; 2024 Wasel. All rights reserved.</p>
                <p>This is an automated email. Please do not reply to this message.</p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          Welcome to Wasel!
          
          Hi ${data.firstName},
          
          Thank you for signing up with Wasel! To complete your registration, please verify your email address by visiting this link:
          
          ${data.verificationUrl}
          
          This verification link will expire in 24 hours for security reasons.
          
          If you didn't create an account with Wasel, please ignore this email.
          
          Best regards,
          The Wasel Team
        `
      };

    case 'passwordReset':
      return {
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password - Wasel</title>
            <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
              .content { padding: 30px 20px; }
              .button { 
                display: inline-block; 
                background: #dc3545; 
                color: white; 
                padding: 12px 30px; 
                text-decoration: none; 
                border-radius: 5px; 
                margin: 20px 0; 
              }
              .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
              .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>Password Reset Request</h1>
              </div>
              <div class="content">
                <h2>Hi ${data.firstName},</h2>
                <p>We received a request to reset your password for your Wasel account. If you made this request, click the button below to reset your password:</p>
                <div style="text-align: center;">
                  <a href="${data.resetUrl}" class="button">Reset Password</a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #dc3545;">${data.resetUrl}</p>
                <div class="warning">
                  <strong>Important:</strong> This password reset link will expire in 1 hour for security reasons.
                </div>
                <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
                <p>For security reasons, we recommend that you:</p>
                <ul>
                  <li>Use a strong, unique password</li>
                  <li>Don't share your password with anyone</li>
                  <li>Enable two-factor authentication if available</li>
                </ul>
                <p>Best regards,<br>The Wasel Team</p>
              </div>
              <div class="footer">
                <p>&copy; 2024 Wasel. All rights reserved.</p>
                <p>This is an automated email. Please do not reply to this message.</p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          Password Reset Request
          
          Hi ${data.firstName},
          
          We received a request to reset your password for your Wasel account. If you made this request, visit this link to reset your password:
          
          ${data.resetUrl}
          
          This password reset link will expire in 1 hour for security reasons.
          
          If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
          
          Best regards,
          The Wasel Team
        `
      };

    case 'welcomeEmail':
      return {
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Wasel!</title>
            <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #28a745; color: white; padding: 20px; text-align: center; }
              .content { padding: 30px 20px; }
              .button { 
                display: inline-block; 
                background: #28a745; 
                color: white; 
                padding: 12px 30px; 
                text-decoration: none; 
                border-radius: 5px; 
                margin: 20px 0; 
              }
              .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
              .feature { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>Welcome to Wasel!</h1>
              </div>
              <div class="content">
                <h2>Hi ${data.firstName},</h2>
                <p>Congratulations! Your email has been verified and your Wasel account is now fully activated.</p>
                <p>You can now enjoy all the features Wasel has to offer:</p>
                <div class="feature">
                  <strong>🍕 Food Delivery</strong><br>
                  Order from your favorite restaurants and get food delivered to your doorstep.
                </div>
                <div class="feature">
                  <strong>👕 Shopping</strong><br>
                  Browse and shop from various stores including clothing, electronics, and more.
                </div>
                <div class="feature">
                  <strong>💊 Pharmacy</strong><br>
                  Order medications and health products with fast delivery.
                </div>
                <div class="feature">
                  <strong>🛒 Supermarket</strong><br>
                  Get groceries and daily essentials delivered to your home.
                </div>
                <div style="text-align: center;">
                  <a href="${process.env.FRONTEND_URL}" class="button">Start Shopping</a>
                </div>
                <p>If you have any questions or need help getting started, feel free to contact our support team.</p>
                <p>Happy shopping!<br>The Wasel Team</p>
              </div>
              <div class="footer">
                <p>&copy; 2024 Wasel. All rights reserved.</p>
                <p>This is an automated email. Please do not reply to this message.</p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          Welcome to Wasel!
          
          Hi ${data.firstName},
          
          Congratulations! Your email has been verified and your Wasel account is now fully activated.
          
          You can now enjoy all the features Wasel has to offer:
          - Food Delivery: Order from your favorite restaurants
          - Shopping: Browse clothing, electronics, and more
          - Pharmacy: Order medications and health products
          - Supermarket: Get groceries delivered to your home
          
          Visit ${process.env.FRONTEND_URL} to start shopping!
          
          If you have any questions, feel free to contact our support team.
          
          Happy shopping!
          The Wasel Team
        `
      };

    default:
      throw new Error(`Unknown email template: ${template}`);
  }
};

// Send email function
export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    const transporter = createTransporter();
    
    // Get email template
    const { html, text } = getEmailTemplate(options.template, options.data);
    
    // Email options
    const mailOptions = {
      from: `"Wasel" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: options.to,
      subject: options.subject,
      html,
      text,
      attachments: options.attachments
    };
    
    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    logger.info(`Email sent successfully to ${options.to}`, {
      messageId: info.messageId,
      template: options.template
    });
    
  } catch (error) {
    logger.error('Failed to send email:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      to: options.to,
      template: options.template
    });
    throw error;
  }
};

// Verify email configuration
export const verifyEmailConfig = async (): Promise<boolean> => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    logger.info('Email configuration verified successfully');
    return true;
  } catch (error) {
    logger.error('Email configuration verification failed:', error);
    return false;
  }
};
