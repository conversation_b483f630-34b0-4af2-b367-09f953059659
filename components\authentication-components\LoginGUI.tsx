import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, H6, Separator, Theme } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link } from 'expo-router';
import Login<PERSON>ogo from '../LoginLogo';

type LoginGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
}

export const LoginGUI = ({ children, methods }: LoginGUIProps) => {
    return (
        <Theme name="light">
              <FormProvider {...methods}>
                <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
                  <Separator />
                  <LoginLogo />
                  <CustomTextField name="username or email" icon="person" label="UserName or Email" placeholder="Enter your username or email" />
                  <CustomTextField name="password" icon="lock-closed" label="Password" secureTextEntry placeholder="Enter your password" />
                  <Link style={{color: "#0063FF",  textDecorationLine: 'underline', padding: 8}} href={{ pathname: '/authentication/verify-before-reset1', params: { name: 'Dan' } }} asChild>
                      <H6>Forgot Password? Click Here</H6>
                  </Link>
                  <XStack justifyContent="center" alignItems="center" gap="$2" padding={"2%"} width={'100%'}>
                    <Link href={{ pathname: '/(customer-pages)/home', params: { name: 'Dan' } }} asChild>
                      <Button width={'45%'} title="Login" /*onPress={onSubmit} - for future (link removed in future)*/></Button>
                    </Link>
                    <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                      <Button width={'45%'} title="Signup" style={{ backgroundColor: '#67B329', color:"#FFFFFF" }} />
                    </Link>
                  </XStack>
                  {children}
                </YStack>
                
              </FormProvider>
        </Theme>
    );
};