import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, XS<PERSON>ck, H6, <PERSON><PERSON><PERSON>, <PERSON>, Button, Text } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FormProvider, useForm } from 'react-hook-form';
import { <PERSON>, router } from 'expo-router';
import <PERSON><PERSON><PERSON>ogo from '../LoginLogo';
import { useAuthStore } from '../../store/store';
import { Alert } from 'react-native';

type LoginFormData = {
  emailOrPhone: string;
  password: string;
};

type LoginGUIProps = {
  children?: React.ReactNode;
};

export const LoginGUI = ({ children }: LoginGUIProps) => {
  const { login, isLoading } = useAuthStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const methods = useForm<LoginFormData>({
    mode: 'onChange',
  });

  const { handleSubmit, formState: { isValid } } = methods;

  const onSubmit = async (data: LoginFormData) => {
    if (isSubmitting || isLoading) return;

    setIsSubmitting(true);

    try {
      const result = await login(data.emailOrPhone, data.password);

      if (result.success) {
        // Navigate to home screen
        router.replace('/(customer-pages)/home');
      } else {
        Alert.alert('Login Failed', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Theme name="light">
      <FormProvider {...methods}>
        <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
          <Separator />
          <LoginLogo />

          {/* Default Credentials Info */}
          <YStack
            backgroundColor="$blue2"
            padding="$3"
            borderRadius="$4"
            borderWidth={1}
            borderColor="$blue6"
            width="90%"
            maxWidth={400}
          >
            <Text textAlign="center" color="$blue11" fontSize="$3" fontWeight="600" marginBottom="$2">
              Default Login Credentials
            </Text>
            <Text textAlign="center" color="$blue10" fontSize="$2">
              📧 Email: <EMAIL>
            </Text>
            <Text textAlign="center" color="$blue10" fontSize="$2">
              🔑 Password: admin123
            </Text>
          </YStack>

          <CustomTextField
            name="emailOrPhone"
            icon="person"
            label="Email or Phone"
            placeholder="Enter your email or phone number"
            keyboardType="email-address"
            autoCapitalize="none"
            rules={{
              required: 'Email or phone is required',
              minLength: {
                value: 3,
                message: 'Please enter a valid email or phone number',
              },
            }}
          />

          <CustomTextField
            name="password"
            icon="lock-closed"
            label="Password"
            secureTextEntry
            placeholder="Enter your password"
            rules={{
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
          />

          <Link
            style={{color: "#0063FF", textDecorationLine: 'underline', padding: 8}}
            href={{ pathname: '/authentication/verify-before-reset1', params: { name: 'Dan' } }}
            asChild
          >
            <H6>Forgot Password? Click Here</H6>
          </Link>

          {/* Login Button */}
          <Button
            size="$5"
            backgroundColor="$purple10"
            color="white"
            onPress={handleSubmit(onSubmit)}
            disabled={!isValid || isSubmitting || isLoading}
            opacity={isValid && !isSubmitting && !isLoading ? 1 : 0.5}
            width="100%"
          >
            {isSubmitting || isLoading ? 'Signing In...' : 'Sign In'}
          </Button>

          {/* Sign Up Link */}
          <XStack justifyContent="center" paddingTop="$2">
            <Text color="$gray10" fontSize="$3">
              Don't have an account?{' '}
            </Text>
            <Text
              color="$purple10"
              fontSize="$3"
              fontWeight="bold"
              onPress={() => router.push('/authentication/signup')}
            >
              Sign Up
            </Text>
          </XStack>

          {children}
        </YStack>
      </FormProvider>
    </Theme>
  );
};