import Joi from 'joi';

// Common validation schemas
const phoneSchema = Joi.string()
  .pattern(/^\+?[1-9]\d{1,14}$/)
  .required()
  .messages({
    'string.pattern.base': 'Please enter a valid phone number with country code',
    'any.required': 'Phone number is required'
  });

const passwordSchema = Joi.string()
  .min(8)
  .max(128)
  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .required()
  .messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.max': 'Password cannot exceed 128 characters',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    'any.required': 'Password is required'
  });

const emailSchema = Joi.string()
  .email()
  .lowercase()
  .required()
  .messages({
    'string.email': 'Please enter a valid email address',
    'any.required': 'Email is required'
  });

const nameSchema = Joi.string()
  .min(2)
  .max(50)
  .pattern(/^[a-zA-Z\u0600-\u06FF\s]+$/)
  .trim()
  .required()
  .messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name cannot exceed 50 characters',
    'string.pattern.base': 'Name can only contain letters and spaces',
    'any.required': 'Name is required'
  });

// Address validation schema
const addressSchema = Joi.object({
  type: Joi.string().valid('home', 'work', 'other').required(),
  label: Joi.string().max(50).trim().optional(),
  street: Joi.string().min(5).max(200).trim().required(),
  building: Joi.string().max(50).trim().optional(),
  floor: Joi.string().max(10).trim().optional(),
  apartment: Joi.string().max(20).trim().optional(),
  city: Joi.string().min(2).max(50).trim().required(),
  area: Joi.string().min(2).max(50).trim().required(),
  landmark: Joi.string().max(100).trim().optional(),
  coordinates: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }).required()
});

// Notifications preferences schema
const notificationsSchema = Joi.object({
  email: Joi.boolean().optional(),
  sms: Joi.boolean().optional(),
  push: Joi.boolean().optional(),
  orderUpdates: Joi.boolean().optional(),
  promotions: Joi.boolean().optional(),
  newsletter: Joi.boolean().optional()
});

// Signup validation
export const validateSignup = (data: any) => {
  const schema = Joi.object({
    firstName: nameSchema,
    lastName: nameSchema,
    email: emailSchema,
    phone: phoneSchema,
    password: passwordSchema,
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': 'Passwords do not match',
        'any.required': 'Password confirmation is required'
      }),
    dateOfBirth: Joi.date()
      .max('now')
      .min('1900-01-01')
      .optional()
      .messages({
        'date.max': 'Date of birth cannot be in the future',
        'date.min': 'Please enter a valid date of birth'
      }),
    gender: Joi.string().valid('male', 'female', 'other').optional(),
    language: Joi.string().valid('en', 'ar').default('en'),
    currency: Joi.string().valid('USD', 'ILS', 'JOD').default('USD'),
    address: addressSchema.optional(),
    notifications: notificationsSchema.optional(),
    termsAccepted: Joi.boolean()
      .valid(true)
      .required()
      .messages({
        'any.only': 'You must accept the terms and conditions',
        'any.required': 'You must accept the terms and conditions'
      }),
    privacyAccepted: Joi.boolean()
      .valid(true)
      .required()
      .messages({
        'any.only': 'You must accept the privacy policy',
        'any.required': 'You must accept the privacy policy'
      }),
    marketingConsent: Joi.boolean().default(false)
  });

  return schema.validate(data, { abortEarly: false });
};

// Login validation
export const validateLogin = (data: any) => {
  const schema = Joi.object({
    emailOrPhone: Joi.alternatives()
      .try(
        Joi.string().email().lowercase(),
        Joi.string().pattern(/^\+?[1-9]\d{1,14}$/)
      )
      .required()
      .messages({
        'alternatives.match': 'Please enter a valid email or phone number',
        'any.required': 'Email or phone number is required'
      }),
    password: Joi.string()
      .min(1)
      .required()
      .messages({
        'any.required': 'Password is required'
      }),
    fcmToken: Joi.string().optional(),
    rememberMe: Joi.boolean().default(false)
  });

  return schema.validate(data);
};

// Forgot password validation
export const validateForgotPassword = (data: any) => {
  const schema = Joi.object({
    email: emailSchema
  });

  return schema.validate(data);
};

// Reset password validation
export const validateResetPassword = (data: any) => {
  const schema = Joi.object({
    token: Joi.string().required().messages({
      'any.required': 'Reset token is required'
    }),
    password: passwordSchema,
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': 'Passwords do not match',
        'any.required': 'Password confirmation is required'
      })
  });

  return schema.validate(data);
};

// Email verification validation
export const validateVerifyEmail = (data: any) => {
  const schema = Joi.object({
    token: Joi.string().required().messages({
      'any.required': 'Verification token is required'
    })
  });

  return schema.validate(data);
};

// Phone verification validation
export const validateVerifyPhone = (data: any) => {
  const schema = Joi.object({
    phone: phoneSchema,
    code: Joi.string()
      .length(6)
      .pattern(/^\d{6}$/)
      .required()
      .messages({
        'string.length': 'Verification code must be 6 digits',
        'string.pattern.base': 'Verification code must contain only numbers',
        'any.required': 'Verification code is required'
      })
  });

  return schema.validate(data);
};

// Update profile validation
export const validateUpdateProfile = (data: any) => {
  const schema = Joi.object({
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    dateOfBirth: Joi.date()
      .max('now')
      .min('1900-01-01')
      .optional(),
    gender: Joi.string().valid('male', 'female', 'other').optional(),
    language: Joi.string().valid('en', 'ar').optional(),
    currency: Joi.string().valid('USD', 'ILS', 'JOD').optional(),
    notifications: notificationsSchema.optional()
  });

  return schema.validate(data);
};

// Change password validation
export const validateChangePassword = (data: any) => {
  const schema = Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'Current password is required'
    }),
    newPassword: passwordSchema,
    confirmPassword: Joi.string()
      .valid(Joi.ref('newPassword'))
      .required()
      .messages({
        'any.only': 'Passwords do not match',
        'any.required': 'Password confirmation is required'
      })
  });

  return schema.validate(data);
};

// Add address validation
export const validateAddAddress = (data: any) => {
  return addressSchema.validate(data);
};

// Update address validation
export const validateUpdateAddress = (data: any) => {
  const schema = addressSchema.keys({
    type: Joi.string().valid('home', 'work', 'other').optional(),
    street: Joi.string().min(5).max(200).trim().optional(),
    city: Joi.string().min(2).max(50).trim().optional(),
    area: Joi.string().min(2).max(50).trim().optional(),
    coordinates: Joi.object({
      latitude: Joi.number().min(-90).max(90).optional(),
      longitude: Joi.number().min(-180).max(180).optional()
    }).optional()
  });

  return schema.validate(data);
};

// Supplier registration validation
export const validateSupplierRegistration = (data: any) => {
  const schema = Joi.object({
    name: Joi.string().min(2).max(100).trim().required(),
    description: Joi.string().min(10).max(1000).trim().required(),
    category: Joi.string()
      .valid('restaurants', 'clothings', 'supermarkets', 'pharmacies', 'electronics', 'other')
      .required(),
    subcategory: Joi.string().max(50).trim().optional(),
    email: emailSchema,
    phone: phoneSchema,
    whatsapp: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    website: Joi.string().uri().optional(),
    businessLicense: Joi.string().min(5).max(50).trim().required(),
    taxId: Joi.string().max(50).trim().optional(),
    location: Joi.object({
      address: Joi.string().min(10).max(200).trim().required(),
      city: Joi.string().min(2).max(50).trim().required(),
      area: Joi.string().min(2).max(50).trim().required(),
      coordinates: Joi.object({
        latitude: Joi.number().min(-90).max(90).required(),
        longitude: Joi.number().min(-180).max(180).required()
      }).required(),
      deliveryRadius: Joi.number().min(1).max(50).default(5)
    }).required(),
    deliveryTime: Joi.object({
      min: Joi.number().min(0).required(),
      max: Joi.number().min(Joi.ref('min')).required()
    }).required(),
    minimumOrder: Joi.number().min(0).default(0),
    deliveryFee: Joi.number().min(0).default(0),
    freeDeliveryThreshold: Joi.number().min(0).optional(),
    tags: Joi.array().items(Joi.string().trim()).max(10).optional(),
    cuisineType: Joi.array().items(Joi.string().trim()).max(5).optional(),
    paymentMethods: Joi.array()
      .items(Joi.string().valid('cash', 'card', 'digital_wallet'))
      .min(1)
      .required()
  });

  return schema.validate(data);
};
