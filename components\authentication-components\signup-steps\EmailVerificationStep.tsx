import React, { useState, useEffect } from 'react';
import { YStack, H5, Text, Button, Input, XStack } from 'tamagui';
import { useAuthStore } from '../../../store/store';
import { useSignupStore } from '../../../store/signupStore';
import { Alert } from 'react-native';

interface EmailVerificationStepProps {
  onComplete: () => void;
}

export const EmailVerificationStep: React.FC<EmailVerificationStepProps> = ({ onComplete }) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const { user, verifyEmail, resendEmailVerification } = useAuthStore();
  const { setEmailVerified, setEmailVerificationSent } = useSignupStore();

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  // Auto-send verification email when component mounts
  useEffect(() => {
    handleResendVerification();
  }, []);

  // Handle verification code submission
  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Error', 'Please enter the verification code');
      return;
    }

    if (verificationCode.length !== 6) {
      Alert.alert('Error', 'Verification code must be 6 digits');
      return;
    }

    setIsVerifying(true);

    try {
      const result = await verifyEmail(verificationCode);
      
      if (result.success) {
        setEmailVerified(true);
        Alert.alert(
          'Email Verified!',
          'Your email has been successfully verified. Welcome to Wasel!',
          [{ text: 'Continue', onPress: onComplete }]
        );
      } else {
        Alert.alert('Verification Failed', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle resend verification email
  const handleResendVerification = async () => {
    setIsResending(true);

    try {
      const result = await resendEmailVerification();
      
      if (result.success) {
        setEmailVerificationSent(true);
        setCountdown(60);
        setCanResend(false);
        Alert.alert('Email Sent', 'A new verification code has been sent to your email.');
      } else {
        Alert.alert('Failed to Send', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to resend verification email. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  // Handle code input change
  const handleCodeChange = (text: string) => {
    // Only allow numbers and limit to 6 digits
    const numericText = text.replace(/[^0-9]/g, '').slice(0, 6);
    setVerificationCode(numericText);
  };

  return (
    <YStack flex={1} gap="$4" alignItems="center" justifyContent="center">
      {/* Header */}
      <YStack gap="$3" alignItems="center" paddingBottom="$4">
        <YStack
          width={80}
          height={80}
          backgroundColor="$purple3"
          borderRadius={40}
          alignItems="center"
          justifyContent="center"
        >
          <Text fontSize="$8">📧</Text>
        </YStack>
        
        <H5 color="$purple10" textAlign="center">
          Verify Your Email
        </H5>
        
        <Text color="$gray10" fontSize="$3" textAlign="center" paddingHorizontal="$4">
          We've sent a 6-digit verification code to
        </Text>
        
        <Text color="$purple10" fontSize="$3" fontWeight="bold" textAlign="center">
          {user?.email}
        </Text>
        
        <Text color="$gray9" fontSize="$2" textAlign="center" paddingHorizontal="$4">
          Please check your email and enter the code below
        </Text>
      </YStack>

      {/* Verification Code Input */}
      <YStack gap="$3" width="100%" alignItems="center">
        <Input
          size="$5"
          fontSize="$6"
          textAlign="center"
          letterSpacing={8}
          fontWeight="bold"
          value={verificationCode}
          onChangeText={handleCodeChange}
          placeholder="000000"
          keyboardType="numeric"
          maxLength={6}
          backgroundColor="$gray2"
          borderColor={verificationCode.length === 6 ? '$purple8' : '$gray7'}
          borderWidth={2}
          width="80%"
        />

        <Text color="$gray9" fontSize="$2" textAlign="center">
          Enter the 6-digit code from your email
        </Text>
      </YStack>

      {/* Verify Button */}
      <Button
        size="$5"
        backgroundColor="$purple10"
        color="white"
        onPress={handleVerifyCode}
        disabled={verificationCode.length !== 6 || isVerifying}
        opacity={verificationCode.length === 6 && !isVerifying ? 1 : 0.5}
        width="80%"
      >
        {isVerifying ? 'Verifying...' : 'Verify Email'}
      </Button>

      {/* Resend Section */}
      <YStack gap="$2" alignItems="center" paddingTop="$4">
        <Text color="$gray9" fontSize="$3" textAlign="center">
          Didn't receive the code?
        </Text>
        
        {canResend ? (
          <Button
            variant="outlined"
            onPress={handleResendVerification}
            disabled={isResending}
          >
            {isResending ? 'Sending...' : 'Resend Code'}
          </Button>
        ) : (
          <Text color="$gray8" fontSize="$3">
            Resend available in {countdown}s
          </Text>
        )}
      </YStack>

      {/* Help Text */}
      <YStack paddingTop="$4" paddingHorizontal="$4">
        <Text color="$gray8" fontSize="$2" textAlign="center">
          Check your spam folder if you don't see the email in your inbox.
          The verification code expires in 10 minutes.
        </Text>
      </YStack>
    </YStack>
  );
};
