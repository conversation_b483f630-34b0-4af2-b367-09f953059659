import { Platform } from 'react-native';

// In-memory fallback storage
class MemoryStorage {
  private storage = new Map<string, string>();

  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async removeItem(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}

// Storage adapter that works across platforms
class StorageAdapter {
  private isWeb = Platform.OS === 'web';
  private asyncStorage: any = null;
  private memoryStorage = new MemoryStorage();
  private initialized = false;

  constructor() {
    // Initialize immediately for non-web platforms
    if (!this.isWeb) {
      this.initializeAsyncStorage();
    }
  }

  private async initializeAsyncStorage() {
    if (this.initialized || this.isWeb) return;

    try {
      // Try to get AsyncStorage from the global scope first
      if (typeof global !== 'undefined' && global.AsyncStorage) {
        this.asyncStorage = global.AsyncStorage;
        this.initialized = true;
        return;
      }

      // Try to import AsyncStorage
      const AsyncStorageModule = require('@react-native-async-storage/async-storage');
      this.asyncStorage = AsyncStorageModule.default || AsyncStorageModule;
      this.initialized = true;
    } catch (error) {
      console.warn('AsyncStorage not available, using memory storage:', error);
      this.asyncStorage = null;
      this.initialized = true;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      if (this.isWeb) {
        return localStorage.getItem(key);
      } else {
        await this.initializeAsyncStorage();
        if (this.asyncStorage) {
          return await this.asyncStorage.getItem(key);
        } else {
          return await this.memoryStorage.getItem(key);
        }
      }
    } catch (error) {
      console.warn('Storage getItem error:', error);
      // Fallback to memory storage
      if (!this.isWeb) {
        return await this.memoryStorage.getItem(key);
      }
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.setItem(key, value);
      } else {
        await this.initializeAsyncStorage();
        if (this.asyncStorage) {
          await this.asyncStorage.setItem(key, value);
        } else {
          await this.memoryStorage.setItem(key, value);
        }
      }
    } catch (error) {
      console.warn('Storage setItem error:', error);
      // Fallback to memory storage
      if (!this.isWeb) {
        await this.memoryStorage.setItem(key, value);
      }
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.removeItem(key);
      } else {
        await this.initializeAsyncStorage();
        if (this.asyncStorage) {
          await this.asyncStorage.removeItem(key);
        } else {
          await this.memoryStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.warn('Storage removeItem error:', error);
      // Fallback to memory storage
      if (!this.isWeb) {
        await this.memoryStorage.removeItem(key);
      }
    }
  }

  async clear(): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.clear();
      } else {
        await this.initializeAsyncStorage();
        if (this.asyncStorage) {
          await this.asyncStorage.clear();
        } else {
          await this.memoryStorage.clear();
        }
      }
    } catch (error) {
      console.warn('Storage clear error:', error);
      // Fallback to memory storage
      if (!this.isWeb) {
        await this.memoryStorage.clear();
      }
    }
  }
}

export const storage = new StorageAdapter();
