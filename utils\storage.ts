import { Platform } from 'react-native';

// Storage adapter that works across platforms
class StorageAdapter {
  private isWeb = Platform.OS === 'web';
  private asyncStorage: any = null;
  private initialized = false;

  private async initializeAsyncStorage() {
    if (this.initialized || this.isWeb) return;

    try {
      // Try to import AsyncStorage
      const AsyncStorageModule = await import('@react-native-async-storage/async-storage');
      this.asyncStorage = AsyncStorageModule.default;
      this.initialized = true;
    } catch (error) {
      console.warn('AsyncStorage not available, falling back to in-memory storage:', error);
      // Fallback to in-memory storage for development
      this.asyncStorage = {
        storage: new Map(),
        async getItem(key: string) {
          return this.storage.get(key) || null;
        },
        async setItem(key: string, value: string) {
          this.storage.set(key, value);
        },
        async removeItem(key: string) {
          this.storage.delete(key);
        },
        async clear() {
          this.storage.clear();
        }
      };
      this.initialized = true;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      if (this.isWeb) {
        return localStorage.getItem(key);
      } else {
        await this.initializeAsyncStorage();
        return await this.asyncStorage.getItem(key);
      }
    } catch (error) {
      console.warn('Storage getItem error:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.setItem(key, value);
      } else {
        await this.initializeAsyncStorage();
        await this.asyncStorage.setItem(key, value);
      }
    } catch (error) {
      console.warn('Storage setItem error:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.removeItem(key);
      } else {
        await this.initializeAsyncStorage();
        await this.asyncStorage.removeItem(key);
      }
    } catch (error) {
      console.warn('Storage removeItem error:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.clear();
      } else {
        await this.initializeAsyncStorage();
        await this.asyncStorage.clear();
      }
    } catch (error) {
      console.warn('Storage clear error:', error);
    }
  }
}

export const storage = new StorageAdapter();
