import React, { useEffect, useState } from 'react';
import { YStack, XStack, H5, Text, Button, Label, Input } from 'tamagui';
import { useForm, FormProvider } from 'react-hook-form';
import { CustomTextField } from '../../CustomTextField';
import { useSignupStore, validateStep2 } from '../../../store/signupStore';
import { SignupStep2Data } from '../../../services/api';

export const SignupStep2 = () => {
  const { step2Data, updateStep2Data, setStepValid } = useSignupStore();

  const methods = useForm<SignupStep2Data>({
    defaultValues: step2Data,
    mode: 'onChange',
  });

  const { watch, setValue, formState: { isValid } } = methods;
  const watchedValues = watch();

  // Update store when form values change
  useEffect(() => {
    updateStep2Data(watchedValues);
    const isStepValid = validateStep2(watchedValues);
    setStepValid(2, isStepValid);
  }, [watchedValues, updateStep2Data, setStepValid]);

  // Handle gender selection
  const handleGenderSelect = (gender: 'male' | 'female' | 'other') => {
    setValue('gender', gender);
  };

  return (
    <FormProvider {...methods}>
      <YStack flex={1} gap="$4">
        {/* Header */}
        <YStack gap="$2" paddingBottom="$3">
          <H5 color="$purple10">Account Details</H5>
          <Text color="$gray10" fontSize="$3">
            Secure your account and tell us about yourself
          </Text>
        </YStack>

        {/* Form Fields */}
        <YStack gap="$3">
          <CustomTextField
            name="password"
            icon="lock-closed"
            label="Password"
            placeholder="Create a strong password"
            secureTextEntry
            rules={{
              required: 'Password is required',
              minLength: {
                value: 8,
                message: 'Password must be at least 8 characters',
              },
              pattern: {
                value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                message: 'Password must contain uppercase, lowercase, and number',
              },
            }}
          />

          <CustomTextField
            name="confirmPassword"
            icon="lock-closed"
            label="Confirm Password"
            placeholder="Confirm your password"
            secureTextEntry
            rules={{
              required: 'Please confirm your password',
              validate: (value: string) => {
                const password = watchedValues.password;
                return value === password || 'Passwords do not match';
              },
            }}
          />

          {/* Date of Birth */}
          <YStack gap="$2">
            <Label color="$gray11" fontSize="$3" fontWeight="600">
              Date of Birth (Optional)
            </Label>
            <Input
              placeholder="YYYY-MM-DD"
              value={watchedValues.dateOfBirth || ''}
              onChangeText={(value) => setValue('dateOfBirth', value)}
              backgroundColor="$gray2"
              borderColor="$gray7"
            />
            <Text color="$gray9" fontSize="$2">
              Format: YYYY-MM-DD (e.g., 1990-01-15)
            </Text>
          </YStack>

          {/* Gender Selection */}
          <YStack gap="$2">
            <Label color="$gray11" fontSize="$3" fontWeight="600">
              Gender (Optional)
            </Label>
            <XStack gap="$2">
              {(['male', 'female', 'other'] as const).map((gender) => (
                <Button
                  key={gender}
                  flex={1}
                  variant={watchedValues.gender === gender ? 'outlined' : 'outlined'}
                  backgroundColor={watchedValues.gender === gender ? '$purple3' : '$gray2'}
                  borderColor={watchedValues.gender === gender ? '$purple8' : '$gray7'}
                  onPress={() => handleGenderSelect(gender)}
                >
                  <Text
                    color={watchedValues.gender === gender ? '$purple11' : '$gray11'}
                    textTransform="capitalize"
                  >
                    {gender}
                  </Text>
                </Button>
              ))}
            </XStack>
          </YStack>
        </YStack>

        {/* Password Requirements */}
        <YStack gap="$1" paddingTop="$2">
          <Text color="$gray9" fontSize="$2" fontWeight="600">
            Password Requirements:
          </Text>
          <Text color="$gray9" fontSize="$2">
            • At least 8 characters long
          </Text>
          <Text color="$gray9" fontSize="$2">
            • Contains uppercase and lowercase letters
          </Text>
          <Text color="$gray9" fontSize="$2">
            • Contains at least one number
          </Text>
        </YStack>
      </YStack>
    </FormProvider>
  );
};
