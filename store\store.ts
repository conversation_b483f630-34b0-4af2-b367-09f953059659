import { create } from 'zustand';
import { authAPI, storage, User, CompleteSignupData } from '../services/api';

// Authentication State
export interface AuthState {
  // State
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;

  // Actions
  login: (emailOrPhone: string, password: string, fcmToken?: string) => Promise<{ success: boolean; message: string }>;
  signup: (data: CompleteSignupData) => Promise<{ success: boolean; message: string; user?: User }>;
  logout: () => Promise<void>;
  verifyEmail: (token: string) => Promise<{ success: boolean; message: string }>;
  resendEmailVerification: () => Promise<{ success: boolean; message: string }>;
  verifyPhone: (code: string) => Promise<{ success: boolean; message: string }>;
  resendPhoneVerification: () => Promise<{ success: boolean; message: string }>;
  loadAuthData: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  accessToken: null,
  refreshToken: null,
  isLoading: false,
  isAuthenticated: false,

  // Login action
  login: async (emailOrPhone: string, password: string, fcmToken?: string) => {
    set({ isLoading: true });

    try {
      const response = await authAPI.login(emailOrPhone, password, fcmToken);

      if (response.success && response.data) {
        const { user, accessToken, refreshToken } = response.data;

        // Save to storage
        await storage.saveAuthData(accessToken, refreshToken, user);

        // Update state
        set({
          user,
          accessToken,
          refreshToken,
          isAuthenticated: true,
          isLoading: false,
        });

        return { success: true, message: 'Login successful' };
      } else {
        set({ isLoading: false });
        return { success: false, message: response.message || 'Login failed' };
      }
    } catch (error: any) {
      set({ isLoading: false });
      const message = error.response?.data?.message || 'Network error. Please try again.';
      return { success: false, message };
    }
  },

  // Signup action
  signup: async (data: CompleteSignupData) => {
    set({ isLoading: true });

    try {
      const response = await authAPI.signup(data);

      if (response.success && response.data) {
        const { user, accessToken, refreshToken } = response.data;

        // Save to storage
        await storage.saveAuthData(accessToken, refreshToken, user);

        // Update state
        set({
          user,
          accessToken,
          refreshToken,
          isAuthenticated: true,
          isLoading: false,
        });

        return { success: true, message: 'Account created successfully', user };
      } else {
        set({ isLoading: false });
        return { success: false, message: response.message || 'Signup failed' };
      }
    } catch (error: any) {
      set({ isLoading: false });
      const message = error.response?.data?.message || 'Network error. Please try again.';
      return { success: false, message };
    }
  },

  // Logout action
  logout: async () => {
    set({ isLoading: true });

    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear storage and state regardless of API response
      await storage.clearAuthData();
      set({
        user: null,
        accessToken: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
      });
    }
  },

  // Verify email action
  verifyEmail: async (token: string) => {
    try {
      const response = await authAPI.verifyEmail(token);

      if (response.success) {
        // Update user's email verification status
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = { ...currentUser, isEmailVerified: true };
          set({ user: updatedUser });
          await storage.saveAuthData(get().accessToken!, get().refreshToken!, updatedUser);
        }
      }

      return { success: response.success, message: response.message };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Verification failed. Please try again.';
      return { success: false, message };
    }
  },

  // Resend email verification
  resendEmailVerification: async () => {
    try {
      const response = await authAPI.resendEmailVerification();
      return { success: response.success, message: response.message };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to resend verification email.';
      return { success: false, message };
    }
  },

  // Verify phone action
  verifyPhone: async (code: string) => {
    try {
      const response = await authAPI.verifyPhone(code);

      if (response.success) {
        // Update user's phone verification status
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = { ...currentUser, isPhoneVerified: true };
          set({ user: updatedUser });
          await storage.saveAuthData(get().accessToken!, get().refreshToken!, updatedUser);
        }
      }

      return { success: response.success, message: response.message };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Phone verification failed. Please try again.';
      return { success: false, message };
    }
  },

  // Resend phone verification
  resendPhoneVerification: async () => {
    try {
      const response = await authAPI.resendPhoneVerification();
      return { success: response.success, message: response.message };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to resend verification code.';
      return { success: false, message };
    }
  },

  // Load auth data from storage
  loadAuthData: async () => {
    set({ isLoading: true });

    try {
      const { accessToken, refreshToken, user } = await storage.getAuthData();

      if (accessToken && refreshToken && user) {
        set({
          user,
          accessToken,
          refreshToken,
          isAuthenticated: true,
          isLoading: false,
        });
      } else {
        set({ isLoading: false });
      }
    } catch (error) {
      console.error('Error loading auth data:', error);
      set({ isLoading: false });
    }
  },

  // Update user data
  updateUser: (userData: Partial<User>) => {
    const currentUser = get().user;
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      set({ user: updatedUser });

      // Save to storage
      storage.saveAuthData(get().accessToken!, get().refreshToken!, updatedUser);
    }
  },

  // Clear auth state
  clearAuth: () => {
    set({
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
    });
  },
}));

// Legacy bear store for backward compatibility
export interface BearState {
  bears: number;
  increasePopulation: () => void;
  removeAllBears: () => void;
  updateBears: (newBears: number) => void;
}

export const useStore = create<BearState>((set) => ({
  bears: 0,
  increasePopulation: () => set((state) => ({ bears: state.bears + 1 })),
  removeAllBears: () => set({ bears: 0 }),
  updateBears: (newBears) => set({ bears: newBears }),
}));
